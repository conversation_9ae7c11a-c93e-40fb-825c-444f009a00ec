# Development Guide

This guide provides comprehensive information for developers working on the MakemyTrip project.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Project Architecture](#project-architecture)
3. [Coding Standards](#coding-standards)
4. [Database Design](#database-design)
5. [API Development](#api-development)
6. [Frontend Development](#frontend-development)
7. [Testing Strategy](#testing-strategy)
8. [Performance Optimization](#performance-optimization)
9. [Security Best Practices](#security-best-practices)
10. [Troubleshooting](#troubleshooting)

## Development Environment Setup

### Prerequisites

- **Node.js** 18+ and npm
- **MongoDB** (local or Atlas)
- **Git** for version control
- **VS Code** (recommended IDE)

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "mongodb.mongodb-vscode",
    "humao.rest-client",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/makemytrip_dev
JWT_SECRET=your-development-jwt-secret
JWT_EXPIRE=30d
STRIPE_SECRET_KEY=sk_test_your_stripe_test_key
OPENAI_API_KEY=sk-your-openai-api-key
CLIENT_URL=http://localhost:3000
```

#### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_key
```

## Project Architecture

### Backend Architecture

```
backend/
├── controllers/         # Route handlers
├── middleware/         # Custom middleware
├── models/            # Database models
├── routes/            # API routes
├── services/          # Business logic
├── utils/             # Utility functions
├── tests/             # Test files
└── server.js          # Entry point
```

### Frontend Architecture

```
frontend/src/
├── components/        # Reusable components
│   ├── common/       # Common UI components
│   ├── forms/        # Form components
│   └── layout/       # Layout components
├── pages/            # Page components
├── store/            # Redux store
│   ├── slices/       # Redux slices
│   └── store.js      # Store configuration
├── services/         # API services
├── hooks/            # Custom hooks
├── utils/            # Utility functions
└── App.js            # Main app component
```

## Coding Standards

### JavaScript/React Standards

1. **Use ES6+ features**
   ```javascript
   // Good
   const handleSubmit = async (data) => {
     try {
       const response = await api.post('/endpoint', data);
       return response.data;
     } catch (error) {
       console.error('Error:', error);
       throw error;
     }
   };
   
   // Avoid
   function handleSubmit(data) {
     return api.post('/endpoint', data)
       .then(function(response) {
         return response.data;
       })
       .catch(function(error) {
         console.error('Error:', error);
         throw error;
       });
   }
   ```

2. **Component Structure**
   ```javascript
   import React, { useState, useEffect } from 'react';
   import { useSelector, useDispatch } from 'react-redux';
   import PropTypes from 'prop-types';
   
   const ComponentName = ({ prop1, prop2 }) => {
     // Hooks
     const [state, setState] = useState(initialState);
     const dispatch = useDispatch();
     const data = useSelector(state => state.data);
   
     // Effects
     useEffect(() => {
       // Effect logic
     }, [dependencies]);
   
     // Event handlers
     const handleEvent = () => {
       // Handler logic
     };
   
     // Render
     return (
       <div>
         {/* Component JSX */}
       </div>
     );
   };
   
   ComponentName.propTypes = {
     prop1: PropTypes.string.isRequired,
     prop2: PropTypes.number
   };
   
   ComponentName.defaultProps = {
     prop2: 0
   };
   
   export default ComponentName;
   ```

3. **Naming Conventions**
   - Components: PascalCase (`FlightCard`)
   - Files: PascalCase for components, camelCase for utilities
   - Variables/Functions: camelCase (`getUserData`)
   - Constants: UPPER_SNAKE_CASE (`API_BASE_URL`)
   - CSS Classes: kebab-case (`flight-card`)

### Backend Standards

1. **Controller Structure**
   ```javascript
   // @desc    Description of the endpoint
   // @route   HTTP_METHOD /api/route
   // @access  Public/Private
   exports.controllerFunction = async (req, res) => {
     try {
       // Validation
       const { error } = validateInput(req.body);
       if (error) {
         return res.status(400).json({
           success: false,
           message: 'Validation failed',
           errors: error.details
         });
       }
   
       // Business logic
       const result = await service.performOperation(req.body);
   
       // Response
       res.status(200).json({
         success: true,
         data: result,
         message: 'Operation successful'
       });
     } catch (error) {
       logger.error('Controller error:', error);
       res.status(500).json({
         success: false,
         message: 'Internal server error'
       });
     }
   };
   ```

2. **Error Handling**
   ```javascript
   // Custom error class
   class AppError extends Error {
     constructor(message, statusCode) {
       super(message);
       this.statusCode = statusCode;
       this.isOperational = true;
       Error.captureStackTrace(this, this.constructor);
     }
   }
   
   // Error middleware
   const errorHandler = (err, req, res, next) => {
     let error = { ...err };
     error.message = err.message;
   
     // Log error
     logger.error(err);
   
     // Mongoose bad ObjectId
     if (err.name === 'CastError') {
       const message = 'Resource not found';
       error = new AppError(message, 404);
     }
   
     // Mongoose duplicate key
     if (err.code === 11000) {
       const message = 'Duplicate field value entered';
       error = new AppError(message, 400);
     }
   
     res.status(error.statusCode || 500).json({
       success: false,
       message: error.message || 'Server Error'
     });
   };
   ```

## Database Design

### Schema Design Principles

1. **Embedded vs Referenced Documents**
   ```javascript
   // Embedded (for small, related data)
   const userSchema = new mongoose.Schema({
     name: String,
     preferences: {
       currency: String,
       language: String,
       notifications: {
         email: Boolean,
         sms: Boolean
       }
     }
   });
   
   // Referenced (for large, independent data)
   const bookingSchema = new mongoose.Schema({
     user: {
       type: mongoose.Schema.Types.ObjectId,
       ref: 'User',
       required: true
     },
     flight: {
       type: mongoose.Schema.Types.ObjectId,
       ref: 'Flight',
       required: true
     }
   });
   ```

2. **Indexing Strategy**
   ```javascript
   // Compound indexes for common queries
   flightSchema.index({ 
     'route.departure.airport.code': 1, 
     'route.arrival.airport.code': 1, 
     'route.departure.dateTime': 1 
   });
   
   // Text indexes for search
   hotelSchema.index({ 
     name: 'text', 
     description: 'text', 
     'location.city': 'text' 
   });
   
   // Unique indexes
   userSchema.index({ email: 1 }, { unique: true });
   ```

## API Development

### RESTful API Design

1. **Resource Naming**
   ```
   GET    /api/flights           # Get all flights
   GET    /api/flights/:id       # Get specific flight
   POST   /api/flights           # Create flight (admin)
   PUT    /api/flights/:id       # Update flight (admin)
   DELETE /api/flights/:id       # Delete flight (admin)
   
   GET    /api/flights/search    # Search flights
   POST   /api/flights/:id/book  # Book flight
   ```

2. **Request/Response Format**
   ```javascript
   // Request
   {
     "from": "JFK",
     "to": "LAX",
     "departureDate": "2024-12-25",
     "passengers": 2
   }
   
   // Response
   {
     "success": true,
     "data": {
       "flights": [...],
       "searchParams": {...}
     },
     "pagination": {
       "currentPage": 1,
       "totalPages": 5,
       "totalItems": 47
     }
   }
   ```

### Middleware Development

1. **Authentication Middleware**
   ```javascript
   const protect = async (req, res, next) => {
     let token;
   
     if (req.headers.authorization?.startsWith('Bearer')) {
       token = req.headers.authorization.split(' ')[1];
     }
   
     if (!token) {
       return res.status(401).json({
         success: false,
         message: 'Not authorized to access this route'
       });
     }
   
     try {
       const decoded = jwt.verify(token, process.env.JWT_SECRET);
       req.user = await User.findById(decoded.id);
       next();
     } catch (error) {
       return res.status(401).json({
         success: false,
         message: 'Not authorized to access this route'
       });
     }
   };
   ```

## Frontend Development

### Component Development

1. **Custom Hooks**
   ```javascript
   // useApi hook
   const useApi = (url, options = {}) => {
     const [data, setData] = useState(null);
     const [loading, setLoading] = useState(true);
     const [error, setError] = useState(null);
   
     useEffect(() => {
       const fetchData = async () => {
         try {
           setLoading(true);
           const response = await api.get(url, options);
           setData(response.data);
         } catch (err) {
           setError(err);
         } finally {
           setLoading(false);
         }
       };
   
       fetchData();
     }, [url]);
   
     return { data, loading, error };
   };
   ```

2. **Form Handling**
   ```javascript
   import { useForm } from 'react-hook-form';
   import { yupResolver } from '@hookform/resolvers/yup';
   import * as yup from 'yup';
   
   const schema = yup.object({
     email: yup.string().email().required(),
     password: yup.string().min(6).required()
   });
   
   const LoginForm = () => {
     const { register, handleSubmit, formState: { errors } } = useForm({
       resolver: yupResolver(schema)
     });
   
     const onSubmit = (data) => {
       // Handle form submission
     };
   
     return (
       <form onSubmit={handleSubmit(onSubmit)}>
         <input {...register('email')} />
         {errors.email && <span>{errors.email.message}</span>}
         
         <input {...register('password')} type="password" />
         {errors.password && <span>{errors.password.message}</span>}
         
         <button type="submit">Submit</button>
       </form>
     );
   };
   ```

### State Management

1. **Redux Slice Structure**
   ```javascript
   import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
   
   // Async thunk
   export const fetchFlights = createAsyncThunk(
     'flights/fetchFlights',
     async (searchParams, { rejectWithValue }) => {
       try {
         const response = await api.get('/flights/search', { params: searchParams });
         return response.data;
       } catch (error) {
         return rejectWithValue(error.response.data);
       }
     }
   );
   
   // Slice
   const flightSlice = createSlice({
     name: 'flights',
     initialState: {
       flights: [],
       loading: false,
       error: null,
       searchParams: {}
     },
     reducers: {
       clearFlights: (state) => {
         state.flights = [];
         state.error = null;
       },
       setSearchParams: (state, action) => {
         state.searchParams = action.payload;
       }
     },
     extraReducers: (builder) => {
       builder
         .addCase(fetchFlights.pending, (state) => {
           state.loading = true;
           state.error = null;
         })
         .addCase(fetchFlights.fulfilled, (state, action) => {
           state.loading = false;
           state.flights = action.payload.data.flights;
         })
         .addCase(fetchFlights.rejected, (state, action) => {
           state.loading = false;
           state.error = action.payload.message;
         });
     }
   });
   
   export const { clearFlights, setSearchParams } = flightSlice.actions;
   export default flightSlice.reducer;
   ```

## Testing Strategy

### Backend Testing

1. **Unit Tests**
   ```javascript
   describe('User Controller', () => {
     describe('POST /api/auth/register', () => {
       it('should register a new user with valid data', async () => {
         const userData = {
           firstName: 'John',
           lastName: 'Doe',
           email: '<EMAIL>',
           password: 'Password123'
         };
   
         const response = await request(app)
           .post('/api/auth/register')
           .send(userData)
           .expect(201);
   
         expect(response.body.success).toBe(true);
         expect(response.body.user.email).toBe(userData.email);
       });
     });
   });
   ```

2. **Integration Tests**
   ```javascript
   describe('Flight Search Integration', () => {
     beforeEach(async () => {
       await Flight.deleteMany({});
       await Flight.create(sampleFlights);
     });
   
     it('should return flights matching search criteria', async () => {
       const response = await request(app)
         .get('/api/flights/search')
         .query({
           from: 'JFK',
           to: 'LAX',
           departureDate: '2024-12-25'
         })
         .expect(200);
   
       expect(response.body.data.flights).toHaveLength(1);
     });
   });
   ```

### Frontend Testing

1. **Component Tests**
   ```javascript
   import { render, screen, fireEvent } from '@testing-library/react';
   import { Provider } from 'react-redux';
   import LoginForm from '../LoginForm';
   import store from '../../store/store';
   
   const renderWithProvider = (component) => {
     return render(
       <Provider store={store}>
         {component}
       </Provider>
     );
   };
   
   describe('LoginForm', () => {
     it('should display validation errors for empty fields', async () => {
       renderWithProvider(<LoginForm />);
       
       const submitButton = screen.getByRole('button', { name: /login/i });
       fireEvent.click(submitButton);
       
       expect(await screen.findByText('Email is required')).toBeInTheDocument();
     });
   });
   ```

## Performance Optimization

### Backend Optimization

1. **Database Optimization**
   ```javascript
   // Use lean queries for read-only operations
   const flights = await Flight.find(query).lean();
   
   // Use select to limit fields
   const users = await User.find().select('name email');
   
   // Use pagination
   const page = parseInt(req.query.page) || 1;
   const limit = parseInt(req.query.limit) || 10;
   const skip = (page - 1) * limit;
   
   const flights = await Flight.find(query)
     .skip(skip)
     .limit(limit);
   ```

2. **Caching Strategy**
   ```javascript
   const redis = require('redis');
   const client = redis.createClient();
   
   const cacheMiddleware = (duration = 300) => {
     return async (req, res, next) => {
       const key = req.originalUrl;
       const cached = await client.get(key);
       
       if (cached) {
         return res.json(JSON.parse(cached));
       }
       
       res.sendResponse = res.json;
       res.json = (body) => {
         client.setex(key, duration, JSON.stringify(body));
         res.sendResponse(body);
       };
       
       next();
     };
   };
   ```

### Frontend Optimization

1. **Code Splitting**
   ```javascript
   import { lazy, Suspense } from 'react';
   
   const FlightSearch = lazy(() => import('./pages/FlightSearch'));
   const HotelSearch = lazy(() => import('./pages/HotelSearch'));
   
   function App() {
     return (
       <Suspense fallback={<div>Loading...</div>}>
         <Routes>
           <Route path="/flights" element={<FlightSearch />} />
           <Route path="/hotels" element={<HotelSearch />} />
         </Routes>
       </Suspense>
     );
   }
   ```

2. **Memoization**
   ```javascript
   import { memo, useMemo, useCallback } from 'react';
   
   const FlightCard = memo(({ flight, onSelect }) => {
     const formattedPrice = useMemo(() => {
       return new Intl.NumberFormat('en-US', {
         style: 'currency',
         currency: 'USD'
       }).format(flight.price);
     }, [flight.price]);
   
     const handleSelect = useCallback(() => {
       onSelect(flight.id);
     }, [flight.id, onSelect]);
   
     return (
       <div onClick={handleSelect}>
         <h3>{flight.airline}</h3>
         <p>{formattedPrice}</p>
       </div>
     );
   });
   ```

## Security Best Practices

1. **Input Validation**
   ```javascript
   const Joi = require('joi');
   
   const validateUser = (data) => {
     const schema = Joi.object({
       email: Joi.string().email().required(),
       password: Joi.string().min(6).required(),
       firstName: Joi.string().min(2).max(50).required()
     });
     
     return schema.validate(data);
   };
   ```

2. **Rate Limiting**
   ```javascript
   const rateLimit = require('express-rate-limit');
   
   const limiter = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100, // limit each IP to 100 requests per windowMs
     message: 'Too many requests from this IP'
   });
   
   app.use('/api/', limiter);
   ```

## Troubleshooting

### Common Issues

1. **CORS Issues**
   ```javascript
   // Backend CORS configuration
   const cors = require('cors');
   
   app.use(cors({
     origin: process.env.CLIENT_URL,
     credentials: true
   }));
   ```

2. **MongoDB Connection Issues**
   ```javascript
   mongoose.connect(process.env.MONGODB_URI, {
     useNewUrlParser: true,
     useUnifiedTopology: true
   }).catch(error => {
     console.error('MongoDB connection error:', error);
     process.exit(1);
   });
   ```

3. **Environment Variable Issues**
   ```javascript
   // Validate required environment variables
   const requiredEnvVars = ['MONGODB_URI', 'JWT_SECRET', 'STRIPE_SECRET_KEY'];
   
   requiredEnvVars.forEach(envVar => {
     if (!process.env[envVar]) {
       console.error(`Missing required environment variable: ${envVar}`);
       process.exit(1);
     }
   });
   ```

For additional development support, refer to the project documentation or contact the development team.
