import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Grid,
  Avatar,
  LinearProgress,
  Skeleton,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  Star,
  ThumbUp,
  ThumbDown,
  Info,
  Refresh
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import api from '../../services/api';

const AIRecommendations = ({ type, searchParams, onRecommendationSelect }) => {
  const { isAuthenticated } = useSelector((state) => state.auth);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isAuthenticated && searchParams) {
      fetchRecommendations();
    }
  }, [isAuthenticated, searchParams, type]);

  const fetchRecommendations = async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const endpoint = type === 'flight' 
        ? '/ai/recommendations/flights'
        : '/ai/recommendations/hotels';

      const response = await api.get(endpoint, { params: searchParams });
      setRecommendations(response.data.data.recommendations || []);
    } catch (error) {
      console.error('Failed to fetch AI recommendations:', error);
      setError('Failed to load AI recommendations');
    } finally {
      setLoading(false);
    }
  };

  const handleRecommendationClick = async (recommendation, index) => {
    try {
      // Track interaction
      if (recommendations.length > 0) {
        await api.post(`/ai/recommendations/${recommendations[0].recommendationId}/interact`, {
          interactionType: 'click',
          additionalData: { recommendationType: recommendation.type, index }
        });
      }

      // Call parent callback
      if (onRecommendationSelect) {
        onRecommendationSelect(recommendation);
      }

      toast.success('Recommendation applied to search');
    } catch (error) {
      console.error('Failed to track recommendation interaction:', error);
    }
  };

  const handleFeedback = async (recommendation, isPositive) => {
    try {
      if (recommendations.length > 0) {
        await api.post(`/ai/recommendations/${recommendations[0].recommendationId}/interact`, {
          interactionType: 'feedback',
          additionalData: { 
            rating: isPositive ? 5 : 1,
            recommendationType: recommendation.type
          }
        });
      }

      toast.success('Thank you for your feedback!');
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      toast.error('Failed to submit feedback');
    }
  };

  const getRecommendationIcon = (recType) => {
    switch (recType) {
      case 'best_value':
      case 'value':
        return <TrendingUp color="success" />;
      case 'fastest':
      case 'premium':
      case 'luxury':
        return <Star color="warning" />;
      case 'location':
        return <Psychology color="info" />;
      default:
        return <Psychology color="primary" />;
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  if (!isAuthenticated) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Psychology sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            AI-Powered Recommendations
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Sign in to get personalized travel recommendations powered by AI
          </Typography>
          <Button variant="contained" href="/login">
            Sign In
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert 
        severity="error" 
        sx={{ mb: 3 }}
        action={
          <IconButton size="small" onClick={fetchRecommendations}>
            <Refresh />
          </IconButton>
        }
      >
        {error}
      </Alert>
    );
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
            <Psychology />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              AI Recommendations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Personalized suggestions based on your preferences
            </Typography>
          </Box>
          <Tooltip title="Refresh recommendations">
            <IconButton onClick={fetchRecommendations} disabled={loading}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>

        {loading ? (
          <Grid container spacing={2}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={4} key={item}>
                <Card variant="outlined">
                  <CardContent>
                    <Skeleton variant="text" width="60%" height={24} />
                    <Skeleton variant="text" width="100%" height={20} />
                    <Skeleton variant="text" width="80%" height={20} />
                    <Box sx={{ mt: 2 }}>
                      <Skeleton variant="rectangular" width="100%" height={36} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : recommendations.length > 0 ? (
          <Grid container spacing={2}>
            {recommendations.map((recommendation, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card 
                    variant="outlined"
                    sx={{
                      height: '100%',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: 4,
                        transform: 'translateY(-2px)'
                      }
                    }}
                    onClick={() => handleRecommendationClick(recommendation, index)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        {getRecommendationIcon(recommendation.type)}
                        <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>
                          {recommendation.title}
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {recommendation.description}
                      </Typography>

                      {/* Confidence Score */}
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            AI Confidence
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {Math.round(recommendation.confidence * 100)}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={recommendation.confidence * 100}
                          color={getConfidenceColor(recommendation.confidence)}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>

                      <Button
                        variant="contained"
                        size="small"
                        fullWidth
                        sx={{ mb: 2 }}
                      >
                        Apply Recommendation
                      </Button>

                      {/* Feedback */}
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                        <Tooltip title="This recommendation was helpful">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFeedback(recommendation, true);
                            }}
                          >
                            <ThumbUp fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="This recommendation was not helpful">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFeedback(recommendation, false);
                            }}
                          >
                            <ThumbDown fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Learn more about AI recommendations">
                          <IconButton size="small">
                            <Info fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Psychology sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body1" color="text.secondary">
              No recommendations available yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Start searching to get personalized AI recommendations
            </Typography>
          </Box>
        )}

        {/* AI Disclaimer */}
        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Info fontSize="small" />
            AI recommendations are based on your preferences and booking history. 
            Results may vary and should be used as guidance only.
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AIRecommendations;
