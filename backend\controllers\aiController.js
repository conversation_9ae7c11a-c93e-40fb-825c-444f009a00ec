const aiService = require('../services/aiService');
const User = require('../models/User');
const Flight = require('../models/Flight');
const Hotel = require('../models/Hotel');
const AIRecommendation = require('../models/AIRecommendation');
const logger = require('../utils/logger');

// @desc    Get AI-powered flight recommendations
// @route   GET /api/ai/recommendations/flights
// @access  Private
exports.getFlightRecommendations = async (req, res) => {
  try {
    const { from, to, departureDate, passengers, travelClass } = req.query;
    
    if (!from || !to || !departureDate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required search parameters'
      });
    }

    // Get user profile for personalization
    const user = await User.findById(req.user.id);
    
    const searchParams = {
      from,
      to,
      departureDate,
      passengers: parseInt(passengers) || 1,
      travelClass: travelClass || 'economy'
    };

    // Get AI recommendations
    const recommendations = await aiService.getFlightRecommendations(user, searchParams);

    // Save recommendations to database for analytics
    const aiRecommendation = new AIRecommendation({
      user: req.user.id,
      type: 'flight',
      category: 'personalized',
      title: 'AI Flight Recommendations',
      description: `Personalized flight recommendations for ${from} to ${to}`,
      aiMetrics: {
        confidenceScore: 0.85,
        relevanceScore: 0.90
      },
      reasoning: {
        factors: [
          { factor: 'past_bookings', weight: 0.3, description: 'Based on travel history' },
          { factor: 'preferences', weight: 0.4, description: 'User preferences' },
          { factor: 'price_trends', weight: 0.3, description: 'Current market trends' }
        ],
        algorithm: 'hybrid'
      },
      validity: {
        validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }
    });

    await aiRecommendation.save();

    res.status(200).json({
      success: true,
      data: {
        recommendations: recommendations.recommendations,
        reasoning: recommendations.reasoning,
        searchParams,
        recommendationId: aiRecommendation._id
      }
    });
  } catch (error) {
    logger.error('Flight recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating flight recommendations'
    });
  }
};

// @desc    Get AI-powered hotel recommendations
// @route   GET /api/ai/recommendations/hotels
// @access  Private
exports.getHotelRecommendations = async (req, res) => {
  try {
    const { destination, checkIn, checkOut, adults, children, rooms } = req.query;
    
    if (!destination || !checkIn || !checkOut) {
      return res.status(400).json({
        success: false,
        message: 'Missing required search parameters'
      });
    }

    const user = await User.findById(req.user.id);
    
    const searchParams = {
      destination,
      checkIn,
      checkOut,
      adults: parseInt(adults) || 2,
      children: parseInt(children) || 0,
      rooms: parseInt(rooms) || 1
    };

    const recommendations = await aiService.getHotelRecommendations(user, searchParams);

    // Save recommendations
    const aiRecommendation = new AIRecommendation({
      user: req.user.id,
      type: 'hotel',
      category: 'personalized',
      title: 'AI Hotel Recommendations',
      description: `Personalized hotel recommendations for ${destination}`,
      aiMetrics: {
        confidenceScore: 0.82,
        relevanceScore: 0.88
      },
      validity: {
        validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000)
      }
    });

    await aiRecommendation.save();

    res.status(200).json({
      success: true,
      data: {
        recommendations: recommendations.recommendations,
        reasoning: recommendations.reasoning,
        searchParams,
        recommendationId: aiRecommendation._id
      }
    });
  } catch (error) {
    logger.error('Hotel recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating hotel recommendations'
    });
  }
};

// @desc    Get AI price predictions
// @route   GET /api/ai/price-predictions/:flightId
// @access  Private
exports.getPricePredictions = async (req, res) => {
  try {
    const { flightId } = req.params;
    
    const flight = await Flight.findById(flightId);
    if (!flight) {
      return res.status(404).json({
        success: false,
        message: 'Flight not found'
      });
    }

    // Get historical prices
    const historicalPrices = flight.aiData.priceHistory || [];

    // Get AI prediction
    const prediction = await aiService.predictPriceChanges(flight, historicalPrices);

    // Update flight with prediction
    flight.aiData.predictedPriceChange = prediction;
    await flight.save();

    res.status(200).json({
      success: true,
      data: {
        flightId,
        currentPrice: flight.pricing.economy.currentPrice,
        prediction,
        historicalPrices: historicalPrices.slice(-7) // Last 7 data points
      }
    });
  } catch (error) {
    logger.error('Price prediction error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating price predictions'
    });
  }
};

// @desc    Chat with AI assistant
// @route   POST /api/ai/chat
// @access  Private
exports.chatWithAI = async (req, res) => {
  try {
    const { message, conversationHistory } = req.body;
    
    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    // Get AI response
    const response = await aiService.getChatbotResponse(message, conversationHistory);

    // Log conversation for analytics
    logger.info(`AI Chat - User: ${req.user.id}, Message: ${message.substring(0, 100)}`);

    res.status(200).json({
      success: true,
      data: {
        response: response.message,
        suggestions: response.suggestions,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('AI chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing chat message'
    });
  }
};

// @desc    Process natural language search
// @route   POST /api/ai/search/natural
// @access  Public
exports.processNaturalLanguageSearch = async (req, res) => {
  try {
    const { query } = req.body;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Process the query with AI
    const parsedQuery = await aiService.processNaturalLanguageSearch(query);

    if (!parsedQuery) {
      return res.status(400).json({
        success: false,
        message: 'Could not understand the search query'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        originalQuery: query,
        parsedQuery,
        suggestions: {
          searchType: parsedQuery.type,
          refinements: [
            'Add specific dates',
            'Specify number of travelers',
            'Add budget preferences'
          ]
        }
      }
    });
  } catch (error) {
    logger.error('Natural language search error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing search query'
    });
  }
};

// @desc    Get travel insights for destination
// @route   GET /api/ai/insights/:destination
// @access  Public
exports.getTravelInsights = async (req, res) => {
  try {
    const { destination } = req.params;
    const { travelDates } = req.query;
    
    if (!destination) {
      return res.status(400).json({
        success: false,
        message: 'Destination is required'
      });
    }

    // Get AI-generated insights
    const insights = await aiService.generateTravelInsights(destination, travelDates);

    res.status(200).json({
      success: true,
      data: {
        destination,
        travelDates,
        insights,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Travel insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating travel insights'
    });
  }
};

// @desc    Get user's AI recommendations history
// @route   GET /api/ai/recommendations/history
// @access  Private
exports.getRecommendationsHistory = async (req, res) => {
  try {
    const { page = 1, limit = 10, type } = req.query;
    
    const query = { user: req.user.id };
    if (type) {
      query.type = type;
    }

    const recommendations = await AIRecommendation.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('type category title description aiMetrics createdAt userInteraction');

    const total = await AIRecommendation.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        recommendations,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalRecommendations: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Recommendations history error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching recommendations history'
    });
  }
};

// @desc    Track recommendation interaction
// @route   POST /api/ai/recommendations/:id/interact
// @access  Private
exports.trackRecommendationInteraction = async (req, res) => {
  try {
    const { id } = req.params;
    const { interactionType, additionalData } = req.body;
    
    const recommendation = await AIRecommendation.findById(id);
    if (!recommendation) {
      return res.status(404).json({
        success: false,
        message: 'Recommendation not found'
      });
    }

    // Track the interaction
    await recommendation.trackInteraction(interactionType, additionalData);

    res.status(200).json({
      success: true,
      message: 'Interaction tracked successfully'
    });
  } catch (error) {
    logger.error('Track interaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Error tracking interaction'
    });
  }
};

// @desc    Get AI analytics dashboard data
// @route   GET /api/ai/analytics
// @access  Private (Admin only)
exports.getAIAnalytics = async (req, res) => {
  try {
    const { dateFrom, dateTo, type } = req.query;
    
    // Get performance analytics
    const analytics = await AIRecommendation.getPerformanceAnalytics({
      dateFrom,
      dateTo,
      type
    });

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('AI analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching AI analytics'
    });
  }
};
