const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Flight = require('../models/Flight');
const Hotel = require('../models/Hotel');
const User = require('../models/User');
const logger = require('../utils/logger');

// Load environment variables
dotenv.config();

// Sample flight data
const sampleFlights = [
  {
    flightNumber: 'AA1234',
    airline: {
      code: 'AA',
      name: 'American Airlines',
      logo: 'https://example.com/aa-logo.png'
    },
    aircraft: {
      type: 'Boeing 737-800',
      model: '737-800',
      capacity: {
        economy: 150,
        premiumEconomy: 20,
        business: 16,
        first: 8
      }
    },
    route: {
      departure: {
        airport: {
          code: 'JFK',
          name: 'John F. Kennedy International Airport',
          city: 'New York',
          country: 'USA',
          timezone: 'America/New_York'
        },
        terminal: '4',
        gate: 'A12',
        dateTime: new Date('2024-12-25T14:30:00Z')
      },
      arrival: {
        airport: {
          code: 'LAX',
          name: 'Los Angeles International Airport',
          city: 'Los Angeles',
          country: 'USA',
          timezone: 'America/Los_Angeles'
        },
        terminal: '6',
        gate: 'B8',
        dateTime: new Date('2024-12-25T18:45:00Z')
      },
      duration: 375, // 6 hours 15 minutes
      distance: 3944,
      stops: []
    },
    pricing: {
      economy: {
        basePrice: 299,
        currentPrice: 329,
        availableSeats: 45,
        taxes: 45,
        fees: 25
      },
      premiumEconomy: {
        basePrice: 499,
        currentPrice: 549,
        availableSeats: 8,
        taxes: 65,
        fees: 35
      },
      business: {
        basePrice: 1299,
        currentPrice: 1399,
        availableSeats: 3,
        taxes: 125,
        fees: 75
      },
      first: {
        basePrice: 2499,
        currentPrice: 2699,
        availableSeats: 1,
        taxes: 225,
        fees: 125
      }
    },
    amenities: {
      wifi: true,
      meals: true,
      entertainment: true,
      powerOutlets: true,
      extraLegroom: false
    },
    baggage: {
      carryOn: {
        included: true,
        weight: 10,
        dimensions: '56x45x25cm'
      },
      checked: {
        included: false,
        weight: 23,
        additionalFee: 30
      }
    },
    policies: {
      cancellation: {
        allowed: true,
        fee: 150,
        timeLimit: 24
      },
      modification: {
        allowed: true,
        fee: 75,
        timeLimit: 24
      },
      refund: {
        allowed: false,
        percentage: 0,
        timeLimit: 0
      }
    },
    status: 'scheduled',
    aiData: {
      priceHistory: [
        { price: 299, date: new Date('2024-11-01'), class: 'economy' },
        { price: 319, date: new Date('2024-11-15'), class: 'economy' },
        { price: 329, date: new Date('2024-12-01'), class: 'economy' }
      ],
      demandScore: 75,
      popularityScore: 85,
      predictedPriceChange: {
        direction: 'up',
        confidence: 0.78,
        percentage: 8
      }
    },
    isActive: true,
    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  },
  {
    flightNumber: 'DL5678',
    airline: {
      code: 'DL',
      name: 'Delta Air Lines',
      logo: 'https://example.com/dl-logo.png'
    },
    aircraft: {
      type: 'Airbus A320',
      model: 'A320',
      capacity: {
        economy: 140,
        premiumEconomy: 18,
        business: 20,
        first: 0
      }
    },
    route: {
      departure: {
        airport: {
          code: 'LAX',
          name: 'Los Angeles International Airport',
          city: 'Los Angeles',
          country: 'USA',
          timezone: 'America/Los_Angeles'
        },
        terminal: '2',
        gate: 'C15',
        dateTime: new Date('2024-12-26T09:15:00Z')
      },
      arrival: {
        airport: {
          code: 'JFK',
          name: 'John F. Kennedy International Airport',
          city: 'New York',
          country: 'USA',
          timezone: 'America/New_York'
        },
        terminal: '4',
        gate: 'A25',
        dateTime: new Date('2024-12-26T17:30:00Z')
      },
      duration: 315, // 5 hours 15 minutes
      distance: 3944,
      stops: []
    },
    pricing: {
      economy: {
        basePrice: 279,
        currentPrice: 299,
        availableSeats: 52,
        taxes: 42,
        fees: 23
      },
      premiumEconomy: {
        basePrice: 459,
        currentPrice: 489,
        availableSeats: 12,
        taxes: 58,
        fees: 32
      },
      business: {
        basePrice: 1199,
        currentPrice: 1299,
        availableSeats: 6,
        taxes: 115,
        fees: 65
      }
    },
    amenities: {
      wifi: true,
      meals: true,
      entertainment: true,
      powerOutlets: true,
      extraLegroom: true
    },
    status: 'scheduled',
    aiData: {
      priceHistory: [
        { price: 279, date: new Date('2024-11-01'), class: 'economy' },
        { price: 289, date: new Date('2024-11-15'), class: 'economy' },
        { price: 299, date: new Date('2024-12-01'), class: 'economy' }
      ],
      demandScore: 68,
      popularityScore: 72,
      predictedPriceChange: {
        direction: 'stable',
        confidence: 0.65,
        percentage: 2
      }
    },
    isActive: true,
    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  }
];

// Sample hotel data
const sampleHotels = [
  {
    name: 'Grand Plaza Hotel',
    description: 'Luxury hotel in the heart of Manhattan with stunning city views and world-class amenities.',
    location: {
      address: {
        street: '123 Broadway',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        zipCode: '10001'
      },
      coordinates: {
        latitude: 40.7589,
        longitude: -73.9851
      },
      nearbyAttractions: [
        { name: 'Times Square', distance: 0.5, type: 'landmark' },
        { name: 'Central Park', distance: 1.2, type: 'park' },
        { name: 'JFK Airport', distance: 25, type: 'airport' }
      ]
    },
    starRating: 5,
    images: [
      {
        url: 'https://example.com/hotel1-main.jpg',
        caption: 'Hotel Exterior',
        isPrimary: true
      },
      {
        url: 'https://example.com/hotel1-lobby.jpg',
        caption: 'Luxury Lobby'
      }
    ],
    amenities: {
      general: ['wifi', 'parking', 'pool', 'gym', 'spa', 'restaurant', 'bar', 'room_service', 'concierge'],
      room: ['air_conditioning', 'tv', 'minibar', 'safe', 'balcony', 'coffee_maker', 'hair_dryer']
    },
    roomTypes: [
      {
        type: 'standard',
        name: 'Standard King Room',
        description: 'Comfortable room with king bed and city view',
        maxOccupancy: {
          adults: 2,
          children: 1,
          total: 3
        },
        bedConfiguration: [
          { type: 'king', count: 1 }
        ],
        size: 35,
        amenities: ['air_conditioning', 'tv', 'minibar', 'safe'],
        pricing: {
          basePrice: 299,
          currency: 'USD',
          taxes: 45,
          fees: 25
        },
        availability: [
          {
            date: new Date('2024-12-20'),
            available: 15,
            price: 329
          },
          {
            date: new Date('2024-12-21'),
            available: 12,
            price: 349
          }
        ],
        totalRooms: 50
      },
      {
        type: 'suite',
        name: 'Executive Suite',
        description: 'Spacious suite with separate living area and premium amenities',
        maxOccupancy: {
          adults: 4,
          children: 2,
          total: 6
        },
        bedConfiguration: [
          { type: 'king', count: 1 },
          { type: 'sofa_bed', count: 1 }
        ],
        size: 75,
        amenities: ['air_conditioning', 'tv', 'minibar', 'safe', 'balcony', 'coffee_maker'],
        pricing: {
          basePrice: 599,
          currency: 'USD',
          taxes: 85,
          fees: 45
        },
        availability: [
          {
            date: new Date('2024-12-20'),
            available: 5,
            price: 649
          },
          {
            date: new Date('2024-12-21'),
            available: 3,
            price: 699
          }
        ],
        totalRooms: 20
      }
    ],
    policies: {
      checkIn: {
        time: '15:00',
        ageRestriction: 18
      },
      checkOut: {
        time: '11:00'
      },
      cancellation: {
        freeUntil: 24,
        fee: 50,
        policy: 'Free cancellation up to 24 hours before check-in'
      },
      children: {
        allowed: true,
        freeAge: 12,
        extraBedFee: 50
      },
      pets: {
        allowed: false
      },
      smoking: {
        allowed: false,
        designatedAreas: false
      }
    },
    contact: {
      phone: '******-123-4567',
      email: '<EMAIL>',
      website: 'https://grandplazahotel.com'
    },
    reviews: {
      averageRating: 4.5,
      totalReviews: 1250,
      breakdown: {
        cleanliness: 4.6,
        comfort: 4.4,
        location: 4.8,
        service: 4.3,
        value: 4.2
      }
    },
    aiData: {
      popularityScore: 88,
      demandForecast: [
        {
          date: new Date('2024-12-20'),
          demand: 'high',
          confidence: 0.85
        }
      ],
      priceRecommendations: [
        {
          roomType: 'standard',
          suggestedPrice: 329,
          reason: 'High demand period',
          date: new Date()
        }
      ],
      competitorAnalysis: {
        averagePrice: 315,
        pricePosition: 'above',
        lastUpdated: new Date()
      }
    },
    isActive: true,
    isVerified: true
  }
];

// Connect to database and seed data
const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logger.info('Connected to MongoDB for seeding');

    // Clear existing data
    await Flight.deleteMany({});
    await Hotel.deleteMany({});
    logger.info('Cleared existing data');

    // Insert sample flights
    await Flight.insertMany(sampleFlights);
    logger.info(`Inserted ${sampleFlights.length} sample flights`);

    // Insert sample hotels
    await Hotel.insertMany(sampleHotels);
    logger.info(`Inserted ${sampleHotels.length} sample hotels`);

    logger.info('Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, sampleFlights, sampleHotels };
