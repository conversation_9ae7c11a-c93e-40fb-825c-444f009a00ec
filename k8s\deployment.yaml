apiVersion: apps/v1
kind: Deployment
metadata:
  name: makemytrip-backend
  namespace: makemytrip
  labels:
    app: makemytrip-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: makemytrip-backend
  template:
    metadata:
      labels:
        app: makemytrip-backend
    spec:
      containers:
      - name: backend
        image: makemytrip/backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: makemytrip-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: makemytrip-config
              key: PORT
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: makemytrip-secrets
              key: MONGODB_URI
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: makemytrip-secrets
              key: JWT_SECRET
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: makemytrip-secrets
              key: STRIPE_SECRET_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: makemytrip-secrets
              key: OPENAI_API_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: makemytrip-backend-service
  namespace: makemytrip
spec:
  selector:
    app: makemytrip-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: makemytrip-frontend
  namespace: makemytrip
  labels:
    app: makemytrip-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: makemytrip-frontend
  template:
    metadata:
      labels:
        app: makemytrip-frontend
    spec:
      containers:
      - name: frontend
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: frontend-build
          mountPath: /usr/share/nginx/html
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: frontend-build
        configMap:
          name: frontend-build
      - name: nginx-config
        configMap:
          name: nginx-config
---
apiVersion: v1
kind: Service
metadata:
  name: makemytrip-frontend-service
  namespace: makemytrip
spec:
  selector:
    app: makemytrip-frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
  type: ClusterIP
