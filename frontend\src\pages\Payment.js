import React, { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Alert
} from '@mui/material';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';

// Components
import PaymentForm from '../components/Payment/PaymentForm';
import PaymentSuccess from '../components/Payment/PaymentSuccess';

// Initialize Stripe
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

const Payment = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [activeStep, setActiveStep] = useState(0);
  const [bookingData, setBookingData] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);
  const [error, setError] = useState(null);

  const steps = ['Review Booking', 'Payment', 'Confirmation'];

  useEffect(() => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { from: location.pathname, bookingData: location.state?.bookingData }
      });
      return;
    }

    // Get booking data from location state
    const bookingDataFromState = location.state?.bookingData;
    
    if (!bookingDataFromState) {
      setError('No booking data found. Please start your booking process again.');
      return;
    }

    setBookingData(bookingDataFromState);
  }, [isAuthenticated, location, navigate]);

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setActiveStep(2); // Move to confirmation step
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    setError(error.message || 'Payment failed. Please try again.');
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        // Review Booking Step
        return (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Box sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                Review Your Booking
              </Typography>
              {bookingData && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {bookingData.type === 'flight' ? 'Flight Booking' : 'Hotel Booking'}
                  </Typography>
                  <Typography variant="body1">
                    Total Amount: ${bookingData.totalAmount}
                  </Typography>
                  {/* Add more booking details here */}
                </Box>
              )}
            </Box>
          </motion.div>
        );

      case 1:
        // Payment Step
        return (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Elements stripe={stripePromise}>
              <PaymentForm
                bookingData={bookingData}
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
              />
            </Elements>
          </motion.div>
        );

      case 2:
        // Confirmation Step
        return (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <PaymentSuccess
              paymentData={paymentResult?.paymentIntent}
              booking={paymentResult?.booking}
              loyaltyPointsEarned={paymentResult?.loyaltyPointsEarned}
            />
          </motion.div>
        );

      default:
        return null;
    }
  };

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Something went wrong
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Please try starting your booking process again.
          </Typography>
        </Box>
      </Container>
    );
  }

  if (!bookingData) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Loading...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, textAlign: 'center', mb: 4 }}>
        Complete Your Booking
      </Typography>

      {/* Progress Stepper */}
      <Box sx={{ mb: 6 }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>

      {/* Step Content */}
      <Box sx={{ minHeight: 400 }}>
        {renderStepContent()}
      </Box>
    </Container>
  );
};

export default Payment;
