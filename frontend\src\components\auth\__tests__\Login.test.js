import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import Login from '../Login';
import authReducer from '../../../store/slices/authSlice';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  Link: ({ children, to, ...props }) => <a href={to} {...props}>{children}</a>
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>
  }
}));

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authReducer
    },
    preloadedState: {
      auth: {
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
        ...initialState.auth
      }
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children, store }) => {
  const theme = createTheme();
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Login Component', () => {
  let store;

  beforeEach(() => {
    store = createTestStore();
    mockNavigate.mockClear();
  });

  it('renders login form correctly', () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByText('Sign in to your MakemyTrip account')).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('displays validation errors for empty fields', async () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });
  });

  it('displays validation error for invalid email format', async () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Invalid email address')).toBeInTheDocument();
    });
  });

  it('displays validation error for short password', async () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: '123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Form should submit without validation errors
    await waitFor(() => {
      expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
      expect(screen.queryByText('Password is required')).not.toBeInTheDocument();
    });
  });

  it('displays loading state when submitting', () => {
    const loadingStore = createTestStore({
      auth: { loading: true }
    });

    render(
      <TestWrapper store={loadingStore}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeDisabled();
  });

  it('displays error message from store', () => {
    const errorStore = createTestStore({
      auth: { error: 'Invalid credentials' }
    });

    render(
      <TestWrapper store={errorStore}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
  });

  it('has social login buttons', () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByText('Continue with Google')).toBeInTheDocument();
    expect(screen.getByText('Continue with Facebook')).toBeInTheDocument();
  });

  it('has forgot password link', () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByText('Forgot your password?')).toBeInTheDocument();
  });

  it('has sign up link', () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    expect(screen.getByText('Sign up')).toBeInTheDocument();
  });

  it('navigates to home page on successful login', async () => {
    // Mock successful login
    const successStore = createTestStore({
      auth: { 
        isAuthenticated: true,
        user: { id: '1', email: '<EMAIL>' },
        token: 'mock-token'
      }
    });

    render(
      <TestWrapper store={successStore}>
        <Login />
      </TestWrapper>
    );

    // In a real test, you would mock the login action and verify navigation
    // This is a simplified test structure
  });

  it('disables submit button when loading', () => {
    const loadingStore = createTestStore({
      auth: { loading: true }
    });

    render(
      <TestWrapper store={loadingStore}>
        <Login />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when not loading', () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    expect(submitButton).not.toBeDisabled();
  });

  it('clears form validation errors when user starts typing', async () => {
    render(
      <TestWrapper store={store}>
        <Login />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Trigger validation error
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });

    // Start typing to clear error
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    await waitFor(() => {
      expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
    });
  });
});
