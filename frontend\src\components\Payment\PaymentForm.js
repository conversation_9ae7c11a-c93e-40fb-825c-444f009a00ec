import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  Divider,
  Grid
} from '@mui/material';
import {
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import api from '../../services/api';

const PaymentForm = ({ 
  bookingData, 
  onPaymentSuccess, 
  onPaymentError,
  isLoading: externalLoading = false 
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const { user } = useSelector((state) => state.auth);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [clientSecret, setClientSecret] = useState('');
  const [paymentIntentId, setPaymentIntentId] = useState('');
  const [bookingId, setBookingId] = useState('');
  const [savePaymentMethod, setSavePaymentMethod] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);

  useEffect(() => {
    if (bookingData && bookingData.totalAmount > 0) {
      createPaymentIntent();
    }
  }, [bookingData]);

  const createPaymentIntent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.post('/payments/create-intent', {
        amount: bookingData.totalAmount,
        currency: bookingData.currency || 'usd',
        bookingData,
        savePaymentMethod
      });

      const { clientSecret, paymentIntentId, bookingId } = response.data.data;
      
      setClientSecret(clientSecret);
      setPaymentIntentId(paymentIntentId);
      setBookingId(bookingId);
    } catch (error) {
      console.error('Failed to create payment intent:', error);
      setError(error.response?.data?.message || 'Failed to initialize payment');
      if (onPaymentError) {
        onPaymentError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsLoading(true);
    setError(null);

    const cardElement = elements.getElement(CardElement);

    try {
      // Confirm payment with Stripe
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${user.firstName} ${user.lastName}`,
              email: user.email,
            },
          },
          setup_future_usage: savePaymentMethod ? 'off_session' : undefined,
        }
      );

      if (stripeError) {
        setError(stripeError.message);
        if (onPaymentError) {
          onPaymentError(stripeError);
        }
        return;
      }

      // Confirm payment on backend
      const confirmResponse = await api.post('/payments/confirm', {
        paymentIntentId: paymentIntent.id,
        bookingId
      });

      if (confirmResponse.data.success) {
        toast.success('Payment successful! Your booking is confirmed.');
        if (onPaymentSuccess) {
          onPaymentSuccess({
            paymentIntent,
            booking: confirmResponse.data.data.booking,
            loyaltyPointsEarned: confirmResponse.data.data.loyaltyPointsEarned
          });
        }
      } else {
        throw new Error(confirmResponse.data.message || 'Payment confirmation failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Payment failed';
      setError(errorMessage);
      toast.error(errorMessage);
      if (onPaymentError) {
        onPaymentError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCardChange = (event) => {
    setCardComplete(event.complete);
    if (event.error) {
      setError(event.error.message);
    } else {
      setError(null);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  };

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  if (!bookingData) {
    return (
      <Alert severity="error">
        Booking data is required to process payment
      </Alert>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Paper elevation={3} sx={{ p: 4, borderRadius: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
          Payment Details
        </Typography>

        {/* Booking Summary */}
        <Box sx={{ mb: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            Booking Summary
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">
                Base Amount:
              </Typography>
            </Grid>
            <Grid item xs={6} sx={{ textAlign: 'right' }}>
              <Typography variant="body2">
                {formatAmount(bookingData.baseAmount || 0, bookingData.currency)}
              </Typography>
            </Grid>
            
            {bookingData.taxes > 0 && (
              <>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Taxes:
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="body2">
                    {formatAmount(bookingData.taxes, bookingData.currency)}
                  </Typography>
                </Grid>
              </>
            )}
            
            {bookingData.fees > 0 && (
              <>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Fees:
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="body2">
                    {formatAmount(bookingData.fees, bookingData.currency)}
                  </Typography>
                </Grid>
              </>
            )}
            
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
            </Grid>
            
            <Grid item xs={6}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Total Amount:
              </Typography>
            </Grid>
            <Grid item xs={6} sx={{ textAlign: 'right' }}>
              <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                {formatAmount(bookingData.totalAmount, bookingData.currency)}
              </Typography>
            </Grid>
          </Grid>
        </Box>

        {/* Payment Form */}
        <form onSubmit={handleSubmit}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
              Card Information
            </Typography>
            <Box
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: error ? 'error.main' : 'grey.300',
                borderRadius: 1,
                '&:focus-within': {
                  borderColor: 'primary.main',
                  borderWidth: 2
                }
              }}
            >
              <CardElement
                options={cardElementOptions}
                onChange={handleCardChange}
              />
            </Box>
          </Box>

          {/* Save Payment Method */}
          <FormControlLabel
            control={
              <Checkbox
                checked={savePaymentMethod}
                onChange={(e) => setSavePaymentMethod(e.target.checked)}
                color="primary"
              />
            }
            label="Save this payment method for future bookings"
            sx={{ mb: 3 }}
          />

          {/* Error Display */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            variant="contained"
            size="large"
            fullWidth
            disabled={!stripe || !cardComplete || isLoading || externalLoading}
            sx={{
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600
            }}
          >
            {isLoading || externalLoading ? (
              <>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                Processing Payment...
              </>
            ) : (
              `Pay ${formatAmount(bookingData.totalAmount, bookingData.currency)}`
            )}
          </Button>
        </form>

        {/* Security Notice */}
        <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="body2" color="info.dark" sx={{ textAlign: 'center' }}>
            🔒 Your payment information is secure and encrypted. 
            We use Stripe for secure payment processing.
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default PaymentForm;
