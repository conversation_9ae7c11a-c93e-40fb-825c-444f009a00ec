const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const paymentController = require('../controllers/paymentController');

const router = express.Router();

// @desc    Create payment intent
// @route   POST /api/payments/create-intent
// @access  Private
router.post('/create-intent', protect, paymentController.createPaymentIntent);

// @desc    Confirm payment
// @route   POST /api/payments/confirm
// @access  Private
router.post('/confirm', protect, paymentController.confirmPayment);

// @desc    Create setup intent for saving payment methods
// @route   POST /api/payments/setup-intent
// @access  Private
router.post('/setup-intent', protect, paymentController.createSetupIntent);

// @desc    Get user's saved payment methods
// @route   GET /api/payments/payment-methods
// @access  Private
router.get('/payment-methods', protect, paymentController.getPaymentMethods);

// @desc    Delete saved payment method
// @route   DELETE /api/payments/payment-methods/:id
// @access  Private
router.delete('/payment-methods/:id', protect, paymentController.deletePaymentMethod);

// @desc    Create refund
// @route   POST /api/payments/refund
// @access  Private
router.post('/refund', protect, paymentController.createRefund);

// @desc    Get payment history
// @route   GET /api/payments/history
// @access  Private
router.get('/history', protect, paymentController.getPaymentHistory);

// @desc    Handle Stripe webhooks
// @route   POST /api/payments/webhook
// @access  Public (verified by Stripe)
router.post('/webhook', express.raw({ type: 'application/json' }), paymentController.handleWebhook);

module.exports = router;
