#!/bin/bash

# MakemyTrip Deployment Script
# This script handles the deployment of the MakemyTrip application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found. Please create it from .env.example"
        exit 1
    fi
    
    log_success "All requirements met"
}

validate_environment() {
    log_info "Validating environment variables..."
    
    # Required environment variables
    required_vars=(
        "JWT_SECRET"
        "MONGODB_URI"
        "STRIPE_SECRET_KEY"
        "OPENAI_API_KEY"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi
    
    log_success "Environment validation passed"
}

build_application() {
    log_info "Building application..."
    
    # Build frontend
    log_info "Building frontend..."
    cd frontend
    npm ci --only=production
    npm run build
    cd ..
    
    # Build Docker images
    log_info "Building Docker images..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    log_success "Application built successfully"
}

deploy_application() {
    log_info "Deploying application..."
    
    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose -f $COMPOSE_FILE down
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose -f $COMPOSE_FILE up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check if services are running
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "Application deployed successfully"
    else
        log_error "Deployment failed. Check container logs."
        docker-compose -f $COMPOSE_FILE logs
        exit 1
    fi
}

run_health_checks() {
    log_info "Running health checks..."
    
    # Check backend health
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:5000/health > /dev/null 2>&1; then
            log_success "Backend health check passed"
            break
        else
            log_info "Attempt $attempt/$max_attempts: Backend not ready yet..."
            sleep 5
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "Backend health check failed"
        exit 1
    fi
    
    # Check frontend
    if curl -f http://localhost:80 > /dev/null 2>&1; then
        log_success "Frontend health check passed"
    else
        log_warning "Frontend health check failed"
    fi
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create logs directory
    mkdir -p logs
    
    # Set up log rotation
    if command -v logrotate &> /dev/null; then
        log_info "Setting up log rotation..."
        # Add logrotate configuration here
    fi
    
    log_success "Monitoring setup completed"
}

cleanup() {
    log_info "Cleaning up..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    log_success "Cleanup completed"
}

show_deployment_info() {
    log_success "Deployment completed successfully!"
    echo ""
    echo "Application URLs:"
    echo "  Frontend: http://localhost:80"
    echo "  Backend API: http://localhost:5000/api"
    echo "  Health Check: http://localhost:5000/health"
    echo ""
    echo "Useful commands:"
    echo "  View logs: docker-compose -f $COMPOSE_FILE logs -f"
    echo "  Stop application: docker-compose -f $COMPOSE_FILE down"
    echo "  Restart application: docker-compose -f $COMPOSE_FILE restart"
    echo ""
}

# Main deployment process
main() {
    log_info "Starting MakemyTrip deployment..."
    log_info "Environment: $ENVIRONMENT"
    
    # Set compose file based on environment
    if [ "$ENVIRONMENT" = "development" ]; then
        COMPOSE_FILE="docker-compose.dev.yml"
    fi
    
    check_requirements
    validate_environment
    build_application
    deploy_application
    run_health_checks
    setup_monitoring
    cleanup
    show_deployment_info
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
