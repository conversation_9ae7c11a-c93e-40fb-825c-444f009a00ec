import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  <PERSON>,
  Divider,
  Card,
  CardContent
} from '@mui/material';
import {
  CheckCircle,
  Download,
  Email,
  Star,
  Flight,
  Hotel
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const PaymentSuccess = ({ paymentData, booking, loyaltyPointsEarned }) => {
  const navigate = useNavigate();

  const formatAmount = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBookingIcon = () => {
    switch (booking?.type) {
      case 'flight':
        return <Flight sx={{ fontSize: 40, color: 'primary.main' }} />;
      case 'hotel':
        return <Hotel sx={{ fontSize: 40, color: 'primary.main' }} />;
      default:
        return <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />;
    }
  };

  const renderBookingDetails = () => {
    if (!booking) return null;

    switch (booking.type) {
      case 'flight':
        return (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Flight Details
              </Typography>
              {booking.flight && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Flight Number:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.flight.flightNumber}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Airline:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.flight.airline}
                    </Typography>
                  </Grid>
                  {booking.flight.route && (
                    <>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          Departure:
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {booking.flight.route.departure?.airport} - {formatDate(booking.flight.route.departure?.dateTime)}
                        </Typography>
                        <Typography variant="body2">
                          {formatTime(booking.flight.route.departure?.dateTime)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary">
                          Arrival:
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {booking.flight.route.arrival?.airport} - {formatDate(booking.flight.route.arrival?.dateTime)}
                        </Typography>
                        <Typography variant="body2">
                          {formatTime(booking.flight.route.arrival?.dateTime)}
                        </Typography>
                      </Grid>
                    </>
                  )}
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Passengers:
                    </Typography>
                    {booking.flight.passengers?.map((passenger, index) => (
                      <Typography key={index} variant="body1">
                        {passenger.title} {passenger.firstName} {passenger.lastName}
                      </Typography>
                    ))}
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        );

      case 'hotel':
        return (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Hotel Details
              </Typography>
              {booking.hotel && (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Hotel:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.hotel.hotelName}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Location:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.hotel.location?.address}, {booking.hotel.location?.city}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Check-in:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {formatDate(booking.hotel.checkIn)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Check-out:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {formatDate(booking.hotel.checkOut)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Nights:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.hotel.nights}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Guests:
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {booking.hotel.totalGuests?.adults} Adults, {booking.hotel.totalGuests?.children} Children
                    </Typography>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6 }}
    >
      <Paper elevation={3} sx={{ p: 4, borderRadius: 3, textAlign: 'center' }}>
        {/* Success Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
        >
          <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
        </motion.div>

        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: 'success.main' }}>
          Payment Successful!
        </Typography>

        <Typography variant="h6" gutterBottom sx={{ mb: 4 }}>
          Your booking has been confirmed
        </Typography>

        {/* Booking Information */}
        <Box sx={{ textAlign: 'left', mb: 4 }}>
          <Card sx={{ mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                {getBookingIcon()}
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                Booking ID: {booking?.bookingId}
              </Typography>
              <Chip 
                label={booking?.status?.toUpperCase()} 
                color="success" 
                sx={{ bgcolor: 'success.main', color: 'white' }}
              />
            </CardContent>
          </Card>

          {renderBookingDetails()}

          {/* Payment Summary */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Payment Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Amount Paid:
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                    {formatAmount(booking?.pricing?.totalAmount, booking?.pricing?.currency)}
                  </Typography>
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Payment Method:
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="body2">
                    {booking?.payment?.method?.replace('_', ' ').toUpperCase()}
                  </Typography>
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Transaction ID:
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                    {booking?.payment?.transactionId?.slice(-8)}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Loyalty Points */}
          {loyaltyPointsEarned > 0 && (
            <Card sx={{ mt: 2, bgcolor: 'warning.light' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Star sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  You earned {loyaltyPointsEarned} loyalty points!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Points have been added to your account
                </Typography>
              </CardContent>
            </Card>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Action Buttons */}
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Button
              variant="outlined"
              fullWidth
              startIcon={<Download />}
              sx={{ py: 1.5 }}
            >
              Download Receipt
            </Button>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Button
              variant="outlined"
              fullWidth
              startIcon={<Email />}
              sx={{ py: 1.5 }}
            >
              Email Confirmation
            </Button>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Button
              variant="contained"
              fullWidth
              onClick={() => navigate('/bookings')}
              sx={{ py: 1.5 }}
            >
              View Bookings
            </Button>
          </Grid>
        </Grid>

        {/* Next Steps */}
        <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
          <Typography variant="h6" gutterBottom>
            What's Next?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • A confirmation email has been sent to {booking?.contactInfo?.email}
            <br />
            • You can view and manage your booking in the "My Bookings" section
            <br />
            • Check-in online 24 hours before your {booking?.type}
            <br />
            • Contact our support team if you need any assistance
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default PaymentSuccess;
