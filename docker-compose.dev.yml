version: '3.8'

services:
  # MongoDB Database for Development
  mongodb-dev:
    image: mongo:6.0
    container_name: makemytrip-mongodb-dev
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: makemytrip_dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb_dev_data:/data/db
    networks:
      - makemytrip-dev-network

  # Redis Cache for Development
  redis-dev:
    image: redis:7-alpine
    container_name: makemytrip-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - makemytrip-dev-network
    command: redis-server --appendonly yes

  # Backend Development Server
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: makemytrip-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      MONGODB_URI: *****************************************************************************
      REDIS_URL: redis://redis-dev:6379
      JWT_SECRET: dev-jwt-secret-key
      JWT_EXPIRE: 30d
      STRIPE_SECRET_KEY: sk_test_your_stripe_test_key
      OPENAI_API_KEY: sk-your-openai-api-key
      CLIENT_URL: http://localhost:3000
    ports:
      - "5000:5000"
    depends_on:
      - mongodb-dev
      - redis-dev
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./logs:/app/logs
    networks:
      - makemytrip-dev-network
    command: npm run dev

  # Frontend Development Server
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: makemytrip-frontend-dev
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
      REACT_APP_STRIPE_PUBLISHABLE_KEY: pk_test_your_stripe_test_key
      CHOKIDAR_USEPOLLING: true
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - makemytrip-dev-network
    command: npm start
    stdin_open: true
    tty: true

volumes:
  mongodb_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  makemytrip-dev-network:
    driver: bridge
