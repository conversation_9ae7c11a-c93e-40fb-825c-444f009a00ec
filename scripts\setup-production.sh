#!/bin/bash

# Production Setup Script for MakemyTrip
# This script sets up the production environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "Please run this script as root or with sudo"
        exit 1
    fi
}

# Update system packages
update_system() {
    log_info "Updating system packages..."
    apt-get update && apt-get upgrade -y
    log_success "System packages updated"
}

# Install Docker
install_docker() {
    log_info "Installing Docker..."
    
    # Remove old versions
    apt-get remove -y docker docker-engine docker.io containerd runc || true
    
    # Install dependencies
    apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Set up stable repository
    echo \
        "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
        $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker Engine
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    log_success "Docker installed successfully"
}

# Install Docker Compose
install_docker_compose() {
    log_info "Installing Docker Compose..."
    
    # Download Docker Compose
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # Make it executable
    chmod +x /usr/local/bin/docker-compose
    
    # Create symlink
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose installed successfully"
}

# Install Node.js
install_nodejs() {
    log_info "Installing Node.js..."
    
    # Install NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    
    # Install Node.js
    apt-get install -y nodejs
    
    log_success "Node.js installed successfully"
}

# Install Nginx
install_nginx() {
    log_info "Installing Nginx..."
    
    apt-get install -y nginx
    
    # Start and enable Nginx
    systemctl start nginx
    systemctl enable nginx
    
    # Configure firewall
    ufw allow 'Nginx Full'
    
    log_success "Nginx installed successfully"
}

# Install SSL certificate (Let's Encrypt)
install_ssl() {
    log_info "Installing SSL certificate..."
    
    # Install Certbot
    apt-get install -y certbot python3-certbot-nginx
    
    log_warning "To obtain SSL certificate, run:"
    log_warning "certbot --nginx -d yourdomain.com -d www.yourdomain.com"
    
    log_success "Certbot installed successfully"
}

# Setup firewall
setup_firewall() {
    log_info "Setting up firewall..."
    
    # Install UFW if not installed
    apt-get install -y ufw
    
    # Reset UFW
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    
    # Allow HTTP and HTTPS
    ufw allow 80
    ufw allow 443
    
    # Allow MongoDB (only from localhost)
    ufw allow from 127.0.0.1 to any port 27017
    
    # Allow Redis (only from localhost)
    ufw allow from 127.0.0.1 to any port 6379
    
    # Enable UFW
    ufw --force enable
    
    log_success "Firewall configured successfully"
}

# Create application user
create_app_user() {
    log_info "Creating application user..."
    
    # Create user if doesn't exist
    if ! id "makemytrip" &>/dev/null; then
        useradd -m -s /bin/bash makemytrip
        usermod -aG docker makemytrip
        log_success "User 'makemytrip' created successfully"
    else
        log_warning "User 'makemytrip' already exists"
    fi
}

# Setup application directory
setup_app_directory() {
    log_info "Setting up application directory..."
    
    # Create application directory
    mkdir -p /opt/makemytrip
    
    # Set ownership
    chown -R makemytrip:makemytrip /opt/makemytrip
    
    # Create logs directory
    mkdir -p /var/log/makemytrip
    chown -R makemytrip:makemytrip /var/log/makemytrip
    
    log_success "Application directory setup completed"
}

# Install monitoring tools
install_monitoring() {
    log_info "Installing monitoring tools..."
    
    # Install htop, iotop, and other monitoring tools
    apt-get install -y htop iotop nethogs ncdu tree
    
    log_success "Monitoring tools installed successfully"
}

# Setup log rotation
setup_log_rotation() {
    log_info "Setting up log rotation..."
    
    cat > /etc/logrotate.d/makemytrip << EOF
/var/log/makemytrip/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 makemytrip makemytrip
    postrotate
        docker-compose -f /opt/makemytrip/docker-compose.yml restart backend
    endscript
}
EOF
    
    log_success "Log rotation configured successfully"
}

# Create systemd service
create_systemd_service() {
    log_info "Creating systemd service..."
    
    cat > /etc/systemd/system/makemytrip.service << EOF
[Unit]
Description=MakemyTrip Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/makemytrip
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0
User=makemytrip
Group=makemytrip

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable makemytrip.service
    
    log_success "Systemd service created successfully"
}

# Main setup function
main() {
    log_info "Starting MakemyTrip production setup..."
    
    check_root
    update_system
    install_docker
    install_docker_compose
    install_nodejs
    install_nginx
    install_ssl
    setup_firewall
    create_app_user
    setup_app_directory
    install_monitoring
    setup_log_rotation
    create_systemd_service
    
    log_success "Production setup completed successfully!"
    
    echo ""
    echo "Next steps:"
    echo "1. Copy your application code to /opt/makemytrip"
    echo "2. Configure environment variables in /opt/makemytrip/.env"
    echo "3. Configure Nginx virtual host"
    echo "4. Obtain SSL certificate: certbot --nginx -d yourdomain.com"
    echo "5. Start the application: systemctl start makemytrip"
    echo ""
    echo "Useful commands:"
    echo "  Start application: systemctl start makemytrip"
    echo "  Stop application: systemctl stop makemytrip"
    echo "  View logs: journalctl -u makemytrip -f"
    echo "  Check status: systemctl status makemytrip"
    echo ""
}

# Run main function
main "$@"
