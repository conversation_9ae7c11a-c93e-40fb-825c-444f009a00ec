apiVersion: v1
kind: Secret
metadata:
  name: makemytrip-secrets
  namespace: makemytrip
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded secrets
  MONGODB_URI: ************************************************************************************
  JWT_SECRET: eW91ci1zdXBlci1zZWN1cmUtand0LXNlY3JldC1rZXktYXQtbGVhc3QtMzItY2hhcmFjdGVycw==
  STRIPE_SECRET_KEY: c2tfbGl2ZV95b3VyX3N0cmlwZV9zZWNyZXRfa2V5
  STRIPE_WEBHOOK_SECRET: d2hzZWNfeW91cl93ZWJob29rX3NlY3JldA==
  OPENAI_API_KEY: c2steW91ci1vcGVuYWktYXBpLWtleQ==
  EMAIL_PASS: eW91ci1zZW5kZ3JpZC1hcGkta2V5
  CLOUDINARY_API_SECRET: eW91ci1jbG91ZGluYXJ5LWFwaS1zZWNyZXQ=
  SESSION_SECRET: eW91ci1zZXNzaW9uLXNlY3JldA==
  REDIS_PASSWORD: eW91ci1yZWRpcy1wYXNzd29yZA==
  SENTRY_DSN: eW91ci1zZW50cnktZHNu
