import React, { useState, useRef } from 'react';
import {
  Box,
  TextField,
  Paper,
  Typography,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Fade,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search,
  Psychology,
  Clear,
  Mic,
  MicOff,
  AutoAwesome
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import api from '../../services/api';

const SmartSearch = ({ onSearchResult, placeholder = "Try: 'Find me a flight from New York to Paris next week'" }) => {
  const [query, setQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedResult, setParsedResult] = useState(null);
  const [error, setError] = useState(null);
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef(null);

  // Initialize speech recognition
  const initSpeechRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setIsListening(true);
      };

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        toast.error('Speech recognition failed');
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  };

  const startListening = () => {
    if (!recognitionRef.current) {
      initSpeechRecognition();
    }

    if (recognitionRef.current) {
      recognitionRef.current.start();
    } else {
      toast.error('Speech recognition not supported in this browser');
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    setIsListening(false);
  };

  const processNaturalLanguageQuery = async () => {
    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setParsedResult(null);

    try {
      const response = await api.post('/ai/search/natural', {
        query: query.trim()
      });

      const result = response.data.data;
      setParsedResult(result);

      // Call parent callback with parsed result
      if (onSearchResult && result.parsedQuery) {
        onSearchResult(result.parsedQuery);
      }

      toast.success('Search query processed successfully!');
    } catch (error) {
      console.error('Natural language search error:', error);
      setError(error.response?.data?.message || 'Failed to process search query');
      toast.error('Failed to process search query');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      processNaturalLanguageQuery();
    }
  };

  const clearSearch = () => {
    setQuery('');
    setParsedResult(null);
    setError(null);
  };

  const exampleQueries = [
    "Flight from NYC to London next Friday",
    "Hotel in Paris for 3 nights in December",
    "Cheap flights to Tokyo for 2 people",
    "Business class flight to Dubai next month",
    "Family hotel near Disneyland for weekend"
  ];

  const handleExampleClick = (example) => {
    setQuery(example);
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* Smart Search Input */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderRadius: 3
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Psychology sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            AI-Powered Smart Search
          </Typography>
          <AutoAwesome sx={{ ml: 1, fontSize: 20 }} />
        </Box>

        <Typography variant="body2" sx={{ mb: 3, opacity: 0.9 }}>
          Describe your travel plans in natural language and let AI understand what you're looking for
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-start' }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isProcessing || isListening}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                '& fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.3)'
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.5)'
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'white'
                }
              },
              '& .MuiInputBase-input::placeholder': {
                color: 'rgba(255, 255, 255, 0.7)',
                opacity: 1
              }
            }}
            InputProps={{
              endAdornment: (
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {query && (
                    <IconButton size="small" onClick={clearSearch} sx={{ color: 'white' }}>
                      <Clear />
                    </IconButton>
                  )}
                  <Tooltip title={isListening ? 'Stop listening' : 'Voice search'}>
                    <IconButton
                      size="small"
                      onClick={isListening ? stopListening : startListening}
                      sx={{ 
                        color: isListening ? 'error.main' : 'white',
                        animation: isListening ? 'pulse 1.5s infinite' : 'none'
                      }}
                    >
                      {isListening ? <MicOff /> : <Mic />}
                    </IconButton>
                  </Tooltip>
                </Box>
              )
            }}
          />

          <Button
            variant="contained"
            onClick={processNaturalLanguageQuery}
            disabled={!query.trim() || isProcessing || isListening}
            sx={{
              minWidth: 120,
              height: 56,
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.3)'
              },
              '&:disabled': {
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                color: 'rgba(255, 255, 255, 0.5)'
              }
            }}
          >
            {isProcessing ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              <>
                <Search sx={{ mr: 1 }} />
                Search
              </>
            )}
          </Button>
        </Box>

        {isListening && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            style={{ marginTop: 16 }}
          >
            <Alert 
              severity="info" 
              sx={{ 
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
                '& .MuiAlert-icon': { color: 'white' }
              }}
            >
              Listening... Speak your travel request clearly
            </Alert>
          </motion.div>
        )}
      </Paper>

      {/* Example Queries */}
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Try these examples:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {exampleQueries.map((example, index) => (
            <Chip
              key={index}
              label={example}
              variant="outlined"
              size="small"
              clickable
              onClick={() => handleExampleClick(example)}
              sx={{ fontSize: '0.75rem' }}
            />
          ))}
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Fade in={true}>
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        </Fade>
      )}

      {/* Parsed Result Display */}
      {parsedResult && (
        <Fade in={true}>
          <Paper elevation={2} sx={{ mt: 2, p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Psychology color="primary" />
              AI Understanding
            </Typography>

            <Typography variant="body2" color="text.secondary" gutterBottom>
              Here's how I interpreted your request:
            </Typography>

            <Box sx={{ mt: 2 }}>
              {parsedResult.parsedQuery && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {parsedResult.parsedQuery.type && (
                    <Chip label={`Type: ${parsedResult.parsedQuery.type}`} color="primary" size="small" />
                  )}
                  {parsedResult.parsedQuery.from && (
                    <Chip label={`From: ${parsedResult.parsedQuery.from}`} color="secondary" size="small" />
                  )}
                  {parsedResult.parsedQuery.to && (
                    <Chip label={`To: ${parsedResult.parsedQuery.to}`} color="secondary" size="small" />
                  )}
                  {parsedResult.parsedQuery.dates && (
                    <Chip label={`Dates: ${parsedResult.parsedQuery.dates}`} color="info" size="small" />
                  )}
                  {parsedResult.parsedQuery.passengers && (
                    <Chip label={`Passengers: ${parsedResult.parsedQuery.passengers}`} color="success" size="small" />
                  )}
                </Box>
              )}

              {parsedResult.suggestions && (
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Suggestions to improve your search:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {parsedResult.suggestions.refinements?.map((suggestion, index) => (
                      <Chip
                        key={index}
                        label={suggestion}
                        variant="outlined"
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          </Paper>
        </Fade>
      )}

      {/* Add CSS for pulse animation */}
      <style jsx>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }
      `}</style>
    </Box>
  );
};

export default SmartSearch;
