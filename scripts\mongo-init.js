// MongoDB initialization script for Docker
// This script runs when the MongoDB container starts for the first time

// Switch to the application database
db = db.getSiblingDB('makemytrip');

// Create application user
db.createUser({
  user: 'makemytrip_user',
  pwd: 'makemytrip_password',
  roles: [
    {
      role: 'readWrite',
      db: 'makemytrip'
    }
  ]
});

// Create indexes for better performance
print('Creating indexes...');

// Users collection indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ 'loyaltyProgram.tier': 1 });
db.users.createIndex({ createdAt: 1 });

// Flights collection indexes
db.flights.createIndex({ 
  'route.departure.airport.code': 1, 
  'route.arrival.airport.code': 1, 
  'route.departure.dateTime': 1 
});
db.flights.createIndex({ 'airline.code': 1 });
db.flights.createIndex({ 'pricing.economy.currentPrice': 1 });
db.flights.createIndex({ 'route.departure.dateTime': 1 });
db.flights.createIndex({ isActive: 1 });

// Hotels collection indexes
db.hotels.createIndex({ 'location.city': 1 });
db.hotels.createIndex({ starRating: 1 });
db.hotels.createIndex({ 'reviews.averageRating': 1 });
db.hotels.createIndex({ 
  name: 'text', 
  description: 'text', 
  'location.city': 'text' 
});

// Bookings collection indexes
db.bookings.createIndex({ user: 1 });
db.bookings.createIndex({ bookingId: 1 }, { unique: true });
db.bookings.createIndex({ status: 1 });
db.bookings.createIndex({ createdAt: 1 });
db.bookings.createIndex({ 'payment.status': 1 });

// AI Recommendations collection indexes
db.airecommendations.createIndex({ user: 1 });
db.airecommendations.createIndex({ type: 1 });
db.airecommendations.createIndex({ createdAt: 1 });
db.airecommendations.createIndex({ 'validity.validUntil': 1 });

print('Indexes created successfully');

// Insert sample data for development
if (db.getName() === 'makemytrip_dev') {
  print('Inserting sample data for development...');
  
  // Sample airports
  db.airports.insertMany([
    {
      code: 'JFK',
      name: 'John F. Kennedy International Airport',
      city: 'New York',
      country: 'USA',
      timezone: 'America/New_York'
    },
    {
      code: 'LAX',
      name: 'Los Angeles International Airport',
      city: 'Los Angeles',
      country: 'USA',
      timezone: 'America/Los_Angeles'
    },
    {
      code: 'LHR',
      name: 'London Heathrow Airport',
      city: 'London',
      country: 'UK',
      timezone: 'Europe/London'
    },
    {
      code: 'CDG',
      name: 'Charles de Gaulle Airport',
      city: 'Paris',
      country: 'France',
      timezone: 'Europe/Paris'
    },
    {
      code: 'NRT',
      name: 'Narita International Airport',
      city: 'Tokyo',
      country: 'Japan',
      timezone: 'Asia/Tokyo'
    }
  ]);
  
  // Sample airlines
  db.airlines.insertMany([
    {
      code: 'AA',
      name: 'American Airlines',
      logo: 'https://example.com/aa-logo.png'
    },
    {
      code: 'DL',
      name: 'Delta Air Lines',
      logo: 'https://example.com/dl-logo.png'
    },
    {
      code: 'UA',
      name: 'United Airlines',
      logo: 'https://example.com/ua-logo.png'
    },
    {
      code: 'BA',
      name: 'British Airways',
      logo: 'https://example.com/ba-logo.png'
    },
    {
      code: 'AF',
      name: 'Air France',
      logo: 'https://example.com/af-logo.png'
    }
  ]);
  
  print('Sample data inserted successfully');
}

print('MongoDB initialization completed');
