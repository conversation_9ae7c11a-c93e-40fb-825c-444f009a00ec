{"name": "makemytrip-fullstack", "version": "1.0.0", "description": "Production-level MakemyTrip clone with AI integration", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "install-server": "cd backend && npm install", "install-client": "cd frontend && npm install", "install-all": "npm run install-server && npm run install-client", "test": "cd backend && npm test && cd ../frontend && npm test", "start": "cd backend && npm start"}, "keywords": ["makemytrip", "travel", "booking", "mern", "ai", "fullstack"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^8.5.2", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "framer-motion": "^12.18.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "morgan": "^1.10.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "redux": "^5.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "winston": "^3.17.0", "yup": "^1.6.1"}}