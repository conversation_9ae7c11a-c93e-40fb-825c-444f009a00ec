import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link,
  IconButton,
  Divider,
  useTheme
} from '@mui/material';
import {
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  YouTube,
  Email,
  Phone,
  LocationOn
} from '@mui/icons-material';

const Footer = () => {
  const theme = useTheme();

  const footerSections = [
    {
      title: 'Company',
      links: [
        { label: 'About Us', href: '/about' },
        { label: 'Careers', href: '/careers' },
        { label: 'Press', href: '/press' },
        { label: 'Blog', href: '/blog' },
        { label: 'Investor Relations', href: '/investors' }
      ]
    },
    {
      title: 'Support',
      links: [
        { label: 'Help Center', href: '/help' },
        { label: 'Contact Us', href: '/contact' },
        { label: 'Safety', href: '/safety' },
        { label: 'Cancellation Policy', href: '/cancellation' },
        { label: 'Travel Guides', href: '/guides' }
      ]
    },
    {
      title: 'Services',
      links: [
        { label: 'Flights', href: '/flights' },
        { label: 'Hotels', href: '/hotels' },
        { label: 'Car Rentals', href: '/cars' },
        { label: 'Travel Insurance', href: '/insurance' },
        { label: 'Group Bookings', href: '/groups' }
      ]
    },
    {
      title: 'Legal',
      links: [
        { label: 'Privacy Policy', href: '/privacy' },
        { label: 'Terms of Service', href: '/terms' },
        { label: 'Cookie Policy', href: '/cookies' },
        { label: 'Accessibility', href: '/accessibility' },
        { label: 'Sitemap', href: '/sitemap' }
      ]
    }
  ];

  const socialLinks = [
    { icon: <Facebook />, href: 'https://facebook.com/makemytrip', label: 'Facebook' },
    { icon: <Twitter />, href: 'https://twitter.com/makemytrip', label: 'Twitter' },
    { icon: <Instagram />, href: 'https://instagram.com/makemytrip', label: 'Instagram' },
    { icon: <LinkedIn />, href: 'https://linkedin.com/company/makemytrip', label: 'LinkedIn' },
    { icon: <YouTube />, href: 'https://youtube.com/makemytrip', label: 'YouTube' }
  ];

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: theme.palette.grey[900],
        color: 'white',
        py: 6,
        mt: 'auto'
      }}
    >
      <Container maxWidth="lg">
        {/* Main Footer Content */}
        <Grid container spacing={4}>
          {/* Company Info */}
          <Grid item xs={12} md={3}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              MakemyTrip
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: 'grey.300' }}>
              Your trusted travel partner for flights, hotels, and unforgettable experiences worldwide.
            </Typography>
            
            {/* Contact Info */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Email sx={{ mr: 1, fontSize: 16 }} />
              <Typography variant="body2" color="grey.300">
                <EMAIL>
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Phone sx={{ mr: 1, fontSize: 16 }} />
              <Typography variant="body2" color="grey.300">
                +****************
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocationOn sx={{ mr: 1, fontSize: 16 }} />
              <Typography variant="body2" color="grey.300">
                New York, NY 10001
              </Typography>
            </Box>

            {/* Social Links */}
            <Box>
              {socialLinks.map((social) => (
                <IconButton
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{ 
                    color: 'grey.300',
                    '&:hover': { color: 'primary.main' },
                    p: 0.5,
                    mr: 1
                  }}
                  aria-label={social.label}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>
          </Grid>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <Grid item xs={6} sm={3} md={2.25} key={section.title}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                {section.title}
              </Typography>
              <Box>
                {section.links.map((link) => (
                  <Link
                    key={link.label}
                    href={link.href}
                    color="grey.300"
                    underline="none"
                    sx={{
                      display: 'block',
                      py: 0.5,
                      fontSize: '0.875rem',
                      '&:hover': {
                        color: 'primary.main',
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    {link.label}
                  </Link>
                ))}
              </Box>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'grey.700' }} />

        {/* Bottom Footer */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="grey.400">
              © {new Date().getFullYear()} MakemyTrip. All rights reserved.
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
              <Typography variant="body2" color="grey.400">
                Made with ❤️ for travelers worldwide
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* App Download Section */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="grey.400" gutterBottom>
            Download our mobile app for the best travel experience
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
            <Link
              href="#"
              sx={{
                display: 'inline-block',
                '&:hover': { opacity: 0.8 }
              }}
            >
              <img
                src="/images/app-store.png"
                alt="Download on App Store"
                style={{ height: 40 }}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            </Link>
            <Link
              href="#"
              sx={{
                display: 'inline-block',
                '&:hover': { opacity: 0.8 }
              }}
            >
              <img
                src="/images/google-play.png"
                alt="Get it on Google Play"
                style={{ height: 40 }}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
