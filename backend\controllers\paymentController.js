const paymentService = require('../services/paymentService');
const Booking = require('../models/Booking');
const User = require('../models/User');
const logger = require('../utils/logger');

// @desc    Create payment intent for booking
// @route   POST /api/payments/create-intent
// @access  Private
exports.createPaymentIntent = async (req, res) => {
  try {
    const { amount, currency, bookingData, savePaymentMethod } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    // Validate amount
    paymentService.validateAmount(amount, currency);

    // Get or create Stripe customer
    let stripeCustomerId = req.user.stripeCustomerId;
    
    if (!stripeCustomerId) {
      const customer = await paymentService.createCustomer(
        req.user.email,
        `${req.user.firstName} ${req.user.lastName}`,
        { userId: req.user._id.toString() }
      );
      
      stripeCustomerId = customer.id;
      
      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user._id, {
        stripeCustomerId: stripeCustomerId
      });
    }

    // Create payment intent
    const paymentIntent = await paymentService.createPaymentIntent(
      amount,
      currency,
      {
        userId: req.user._id.toString(),
        bookingType: bookingData?.type || 'unknown',
        customerId: stripeCustomerId
      }
    );

    // Create pending booking record
    const booking = new Booking({
      user: req.user._id,
      type: bookingData.type,
      pricing: {
        baseAmount: bookingData.baseAmount || amount,
        taxes: bookingData.taxes || 0,
        fees: bookingData.fees || 0,
        totalAmount: amount
      },
      payment: {
        method: 'credit_card',
        status: 'pending',
        transactionId: paymentIntent.paymentIntentId
      },
      contactInfo: {
        email: req.user.email,
        phone: req.user.phone
      },
      // Add specific booking data based on type
      ...(bookingData.type === 'flight' && { flight: bookingData.flight }),
      ...(bookingData.type === 'hotel' && { hotel: bookingData.hotel }),
      ...(bookingData.type === 'package' && { package: bookingData.package })
    });

    await booking.save();

    res.status(200).json({
      success: true,
      data: {
        clientSecret: paymentIntent.clientSecret,
        paymentIntentId: paymentIntent.paymentIntentId,
        bookingId: booking._id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency
      }
    });
  } catch (error) {
    logger.error('Create payment intent error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create payment intent'
    });
  }
};

// @desc    Confirm payment and complete booking
// @route   POST /api/payments/confirm
// @access  Private
exports.confirmPayment = async (req, res) => {
  try {
    const { paymentIntentId, bookingId } = req.body;

    if (!paymentIntentId || !bookingId) {
      return res.status(400).json({
        success: false,
        message: 'Payment intent ID and booking ID are required'
      });
    }

    // Get payment intent status
    const paymentIntent = await paymentService.getPaymentIntent(paymentIntentId);

    // Find booking
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Verify booking belongs to user
    if (booking.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to booking'
      });
    }

    // Update booking based on payment status
    if (paymentIntent.status === 'succeeded') {
      booking.payment.status = 'completed';
      booking.payment.paidAmount = paymentIntent.amount / 100; // Convert from cents
      booking.payment.paidAt = new Date();
      booking.status = 'confirmed';

      // Award loyalty points
      const pointsEarned = Math.floor(booking.pricing.totalAmount * 10); // 10 points per dollar
      booking.loyaltyPointsEarned = pointsEarned;
      
      // Update user loyalty points
      await req.user.addLoyaltyPoints(pointsEarned);

      await booking.save();

      // Send confirmation email (implement this)
      // await sendBookingConfirmationEmail(booking);

      res.status(200).json({
        success: true,
        message: 'Payment confirmed and booking completed',
        data: {
          booking: booking,
          loyaltyPointsEarned: pointsEarned
        }
      });
    } else {
      booking.payment.status = 'failed';
      booking.payment.failureReason = 'Payment not completed';
      booking.status = 'cancelled';
      
      await booking.save();

      res.status(400).json({
        success: false,
        message: 'Payment was not successful',
        data: {
          paymentStatus: paymentIntent.status
        }
      });
    }
  } catch (error) {
    logger.error('Confirm payment error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to confirm payment'
    });
  }
};

// @desc    Create setup intent for saving payment methods
// @route   POST /api/payments/setup-intent
// @access  Private
exports.createSetupIntent = async (req, res) => {
  try {
    // Get or create Stripe customer
    let stripeCustomerId = req.user.stripeCustomerId;
    
    if (!stripeCustomerId) {
      const customer = await paymentService.createCustomer(
        req.user.email,
        `${req.user.firstName} ${req.user.lastName}`,
        { userId: req.user._id.toString() }
      );
      
      stripeCustomerId = customer.id;
      
      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user._id, {
        stripeCustomerId: stripeCustomerId
      });
    }

    const setupIntent = await paymentService.createSetupIntent(stripeCustomerId);

    res.status(200).json({
      success: true,
      data: setupIntent
    });
  } catch (error) {
    logger.error('Create setup intent error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create setup intent'
    });
  }
};

// @desc    Get user's saved payment methods
// @route   GET /api/payments/payment-methods
// @access  Private
exports.getPaymentMethods = async (req, res) => {
  try {
    if (!req.user.stripeCustomerId) {
      return res.status(200).json({
        success: true,
        data: []
      });
    }

    const paymentMethods = await paymentService.getCustomerPaymentMethods(
      req.user.stripeCustomerId
    );

    res.status(200).json({
      success: true,
      data: paymentMethods
    });
  } catch (error) {
    logger.error('Get payment methods error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to retrieve payment methods'
    });
  }
};

// @desc    Delete saved payment method
// @route   DELETE /api/payments/payment-methods/:id
// @access  Private
exports.deletePaymentMethod = async (req, res) => {
  try {
    const { id } = req.params;

    await paymentService.detachPaymentMethod(id);

    res.status(200).json({
      success: true,
      message: 'Payment method deleted successfully'
    });
  } catch (error) {
    logger.error('Delete payment method error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete payment method'
    });
  }
};

// @desc    Create refund for booking
// @route   POST /api/payments/refund
// @access  Private
exports.createRefund = async (req, res) => {
  try {
    const { bookingId, amount, reason } = req.body;

    if (!bookingId) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID is required'
      });
    }

    // Find booking
    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Verify booking belongs to user or user is admin
    if (booking.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to booking'
      });
    }

    // Check if booking can be refunded
    if (booking.payment.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Booking payment is not completed'
      });
    }

    // Calculate refund amount
    const cancellationFee = booking.calculateCancellationFee();
    const maxRefundAmount = booking.pricing.totalAmount - cancellationFee;
    const refundAmount = amount || maxRefundAmount;

    if (refundAmount > maxRefundAmount) {
      return res.status(400).json({
        success: false,
        message: `Maximum refund amount is ${maxRefundAmount}`
      });
    }

    // Create refund
    const refund = await paymentService.createRefund(
      booking.payment.transactionId,
      refundAmount,
      reason || 'requested_by_customer'
    );

    // Update booking
    booking.payment.refundAmount = refundAmount;
    booking.payment.status = refundAmount >= booking.pricing.totalAmount ? 'refunded' : 'partially_refunded';
    booking.cancellation = {
      isCancelled: true,
      cancelledAt: new Date(),
      cancelledBy: req.user._id,
      reason: reason || 'Customer request',
      refundAmount: refundAmount,
      cancellationFee: cancellationFee
    };
    booking.status = 'cancelled';

    await booking.save();

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      data: {
        refund,
        booking: booking
      }
    });
  } catch (error) {
    logger.error('Create refund error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to process refund'
    });
  }
};

// @desc    Handle Stripe webhooks
// @route   POST /api/payments/webhook
// @access  Public (but verified by Stripe)
exports.handleWebhook = async (req, res) => {
  try {
    const signature = req.headers['stripe-signature'];
    
    await paymentService.handleWebhook(req.body, signature);

    res.status(200).json({ received: true });
  } catch (error) {
    logger.error('Webhook error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Webhook error'
    });
  }
};

// @desc    Get payment history
// @route   GET /api/payments/history
// @access  Private
exports.getPaymentHistory = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const bookings = await Booking.find({ 
      user: req.user._id,
      'payment.status': { $in: ['completed', 'refunded', 'partially_refunded'] }
    })
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .select('bookingId type pricing payment status createdAt');

    const total = await Booking.countDocuments({ 
      user: req.user._id,
      'payment.status': { $in: ['completed', 'refunded', 'partially_refunded'] }
    });

    res.status(200).json({
      success: true,
      data: {
        payments: bookings,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalPayments: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment history'
    });
  }
};
