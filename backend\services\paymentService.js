const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const logger = require('../utils/logger');

class PaymentService {
  constructor() {
    this.stripe = stripe;
  }

  // Create payment intent for booking
  async createPaymentIntent(amount, currency = 'usd', metadata = {}) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      logger.info(`Payment intent created: ${paymentIntent.id} for amount: ${amount} ${currency}`);
      
      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status
      };
    } catch (error) {
      logger.error('Error creating payment intent:', error);
      throw new Error('Failed to create payment intent');
    }
  }

  // Confirm payment intent
  async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      logger.info(`Payment intent confirmed: ${paymentIntentId}`);
      
      return {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        charges: paymentIntent.charges.data
      };
    } catch (error) {
      logger.error('Error confirming payment intent:', error);
      throw new Error('Failed to confirm payment');
    }
  }

  // Retrieve payment intent
  async getPaymentIntent(paymentIntentId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      return {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        metadata: paymentIntent.metadata,
        created: paymentIntent.created
      };
    } catch (error) {
      logger.error('Error retrieving payment intent:', error);
      throw new Error('Failed to retrieve payment intent');
    }
  }

  // Create refund
  async createRefund(paymentIntentId, amount = null, reason = 'requested_by_customer') {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        reason
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100); // Convert to cents
      }

      const refund = await this.stripe.refunds.create(refundData);

      logger.info(`Refund created: ${refund.id} for payment intent: ${paymentIntentId}`);
      
      return {
        id: refund.id,
        amount: refund.amount,
        currency: refund.currency,
        status: refund.status,
        reason: refund.reason
      };
    } catch (error) {
      logger.error('Error creating refund:', error);
      throw new Error('Failed to create refund');
    }
  }

  // Create customer
  async createCustomer(email, name, metadata = {}) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata
      });

      logger.info(`Customer created: ${customer.id} for email: ${email}`);
      
      return {
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: customer.created
      };
    } catch (error) {
      logger.error('Error creating customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  // Get customer
  async getCustomer(customerId) {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      
      return {
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: customer.created,
        metadata: customer.metadata
      };
    } catch (error) {
      logger.error('Error retrieving customer:', error);
      throw new Error('Failed to retrieve customer');
    }
  }

  // Create setup intent for saving payment methods
  async createSetupIntent(customerId) {
    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
      });

      logger.info(`Setup intent created: ${setupIntent.id} for customer: ${customerId}`);
      
      return {
        clientSecret: setupIntent.client_secret,
        setupIntentId: setupIntent.id,
        status: setupIntent.status
      };
    } catch (error) {
      logger.error('Error creating setup intent:', error);
      throw new Error('Failed to create setup intent');
    }
  }

  // List customer payment methods
  async getCustomerPaymentMethods(customerId, type = 'card') {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type,
      });

      return paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        card: pm.card ? {
          brand: pm.card.brand,
          last4: pm.card.last4,
          exp_month: pm.card.exp_month,
          exp_year: pm.card.exp_year
        } : null,
        created: pm.created
      }));
    } catch (error) {
      logger.error('Error retrieving payment methods:', error);
      throw new Error('Failed to retrieve payment methods');
    }
  }

  // Detach payment method
  async detachPaymentMethod(paymentMethodId) {
    try {
      const paymentMethod = await this.stripe.paymentMethods.detach(paymentMethodId);

      logger.info(`Payment method detached: ${paymentMethodId}`);
      
      return {
        id: paymentMethod.id,
        status: 'detached'
      };
    } catch (error) {
      logger.error('Error detaching payment method:', error);
      throw new Error('Failed to detach payment method');
    }
  }

  // Handle webhook events
  async handleWebhook(payload, signature) {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      logger.info(`Webhook received: ${event.type}`);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        case 'charge.dispute.created':
          await this.handleChargeDispute(event.data.object);
          break;
        default:
          logger.info(`Unhandled webhook event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      logger.error('Webhook error:', error);
      throw new Error('Webhook signature verification failed');
    }
  }

  // Handle successful payment
  async handlePaymentSucceeded(paymentIntent) {
    try {
      logger.info(`Payment succeeded: ${paymentIntent.id}`);
      
      // Here you would update your booking status, send confirmation emails, etc.
      // This is handled in the booking controller
      
      return { success: true };
    } catch (error) {
      logger.error('Error handling payment success:', error);
      throw error;
    }
  }

  // Handle failed payment
  async handlePaymentFailed(paymentIntent) {
    try {
      logger.info(`Payment failed: ${paymentIntent.id}`);
      
      // Here you would update booking status, notify user, etc.
      
      return { success: true };
    } catch (error) {
      logger.error('Error handling payment failure:', error);
      throw error;
    }
  }

  // Handle charge dispute
  async handleChargeDispute(dispute) {
    try {
      logger.info(`Charge dispute created: ${dispute.id}`);
      
      // Here you would handle the dispute process
      
      return { success: true };
    } catch (error) {
      logger.error('Error handling charge dispute:', error);
      throw error;
    }
  }

  // Calculate platform fee (for marketplace model)
  calculatePlatformFee(amount, feePercentage = 2.9) {
    return Math.round(amount * (feePercentage / 100) * 100) / 100;
  }

  // Validate amount
  validateAmount(amount, currency = 'usd') {
    const minAmounts = {
      usd: 0.50,
      eur: 0.50,
      gbp: 0.30,
      inr: 0.50
    };

    const minAmount = minAmounts[currency.toLowerCase()] || 0.50;
    
    if (amount < minAmount) {
      throw new Error(`Amount must be at least ${minAmount} ${currency.toUpperCase()}`);
    }

    return true;
  }

  // Format amount for display
  formatAmount(amount, currency = 'usd') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  }
}

module.exports = new PaymentService();
