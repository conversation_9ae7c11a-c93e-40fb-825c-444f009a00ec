const axios = require('axios');
const logger = require('../utils/logger');

class AIService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.openaiBaseUrl = 'https://api.openai.com/v1';
  }

  // AI-powered flight search recommendations
  async getFlightRecommendations(userProfile, searchParams) {
    try {
      const prompt = this.buildFlightRecommendationPrompt(userProfile, searchParams);
      
      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a travel expert AI assistant that provides personalized flight recommendations based on user preferences and travel history.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      });

      return this.parseFlightRecommendations(response.choices[0].message.content);
    } catch (error) {
      logger.error('AI flight recommendations error:', error);
      return this.getFallbackFlightRecommendations();
    }
  }

  // AI-powered hotel recommendations
  async getHotelRecommendations(userProfile, searchParams) {
    try {
      const prompt = this.buildHotelRecommendationPrompt(userProfile, searchParams);
      
      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a travel expert AI assistant that provides personalized hotel recommendations based on user preferences, budget, and travel purpose.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      });

      return this.parseHotelRecommendations(response.choices[0].message.content);
    } catch (error) {
      logger.error('AI hotel recommendations error:', error);
      return this.getFallbackHotelRecommendations();
    }
  }

  // AI-powered price prediction
  async predictPriceChanges(flightData, historicalPrices) {
    try {
      const prompt = this.buildPricePredictionPrompt(flightData, historicalPrices);
      
      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a data analyst AI that predicts flight price changes based on historical data, seasonality, and market trends.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.3
      });

      return this.parsePricePrediction(response.choices[0].message.content);
    } catch (error) {
      logger.error('AI price prediction error:', error);
      return this.getFallbackPricePrediction();
    }
  }

  // AI chatbot for customer support
  async getChatbotResponse(userMessage, conversationHistory = []) {
    try {
      const systemPrompt = `You are a helpful customer support AI for MakemyTrip, a travel booking platform. 
      You can help users with:
      - Flight and hotel bookings
      - Travel recommendations
      - Booking modifications and cancellations
      - General travel advice
      - Platform navigation
      
      Be friendly, helpful, and professional. If you cannot help with something, direct them to human support.`;

      const messages = [
        { role: 'system', content: systemPrompt },
        ...conversationHistory.slice(-10), // Keep last 10 messages for context
        { role: 'user', content: userMessage }
      ];

      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 400,
        temperature: 0.8
      });

      return {
        message: response.choices[0].message.content,
        suggestions: this.generateSuggestions(userMessage)
      };
    } catch (error) {
      logger.error('AI chatbot error:', error);
      return {
        message: "I'm sorry, I'm having trouble processing your request right now. Please try again or contact our human support team.",
        suggestions: ['Contact Support', 'Try Again', 'Browse Help Center']
      };
    }
  }

  // Smart search with natural language processing
  async processNaturalLanguageSearch(query) {
    try {
      const prompt = `Parse this travel search query and extract structured information:
      Query: "${query}"
      
      Extract and return JSON with:
      - type: "flight" or "hotel" or "package"
      - from: departure city/airport
      - to: destination city/airport
      - dates: travel dates mentioned
      - passengers: number of travelers
      - preferences: any specific requirements
      - budget: budget range if mentioned`;

      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a travel search parser that converts natural language queries into structured search parameters.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.2
      });

      return this.parseSearchQuery(response.choices[0].message.content);
    } catch (error) {
      logger.error('AI search parsing error:', error);
      return null;
    }
  }

  // Generate travel insights and tips
  async generateTravelInsights(destination, travelDates) {
    try {
      const prompt = `Provide travel insights for ${destination} during ${travelDates}:
      - Weather conditions
      - Best attractions to visit
      - Local customs and tips
      - Budget recommendations
      - Safety considerations
      Keep it concise and practical.`;

      const response = await this.callOpenAI({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a knowledgeable travel advisor providing practical insights for travelers.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 400,
        temperature: 0.7
      });

      return this.parseTravelInsights(response.choices[0].message.content);
    } catch (error) {
      logger.error('AI travel insights error:', error);
      return this.getFallbackTravelInsights();
    }
  }

  // Helper method to call OpenAI API
  async callOpenAI(payload) {
    if (!this.openaiApiKey || this.openaiApiKey === 'demo_openai_key') {
      throw new Error('OpenAI API key not configured');
    }

    const response = await axios.post(`${this.openaiBaseUrl}/chat/completions`, payload, {
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    return response.data;
  }

  // Build prompts for different AI services
  buildFlightRecommendationPrompt(userProfile, searchParams) {
    return `User Profile:
    - Travel history: ${userProfile.travelHistory?.length || 0} previous trips
    - Preferred class: ${userProfile.preferences?.travelClass || 'economy'}
    - Budget preference: ${userProfile.preferences?.budget || 'moderate'}
    - Loyalty tier: ${userProfile.loyaltyProgram?.tier || 'bronze'}
    
    Search Parameters:
    - From: ${searchParams.from}
    - To: ${searchParams.to}
    - Date: ${searchParams.departureDate}
    - Passengers: ${searchParams.passengers}
    
    Provide 3 personalized flight recommendations with reasons.`;
  }

  buildHotelRecommendationPrompt(userProfile, searchParams) {
    return `User Profile:
    - Previous hotel stays: ${userProfile.travelHistory?.length || 0}
    - Preferred amenities: ${userProfile.preferences?.amenities?.join(', ') || 'standard'}
    - Budget range: ${userProfile.preferences?.budget || 'moderate'}
    
    Search Parameters:
    - Destination: ${searchParams.destination}
    - Check-in: ${searchParams.checkIn}
    - Check-out: ${searchParams.checkOut}
    - Guests: ${searchParams.adults} adults, ${searchParams.children} children
    
    Recommend 3 hotels with explanations for each choice.`;
  }

  buildPricePredictionPrompt(flightData, historicalPrices) {
    return `Flight: ${flightData.airline} ${flightData.flightNumber}
    Route: ${flightData.route.departure.airport.code} to ${flightData.route.arrival.airport.code}
    Current price: $${flightData.pricing.economy.currentPrice}
    
    Historical prices (last 30 days): ${historicalPrices.map(p => `$${p.price} on ${p.date}`).join(', ')}
    
    Predict if prices will go up, down, or stay stable in the next 7 days. Provide confidence level and reasoning.`;
  }

  // Parse AI responses
  parseFlightRecommendations(response) {
    // In a real implementation, this would parse the AI response more intelligently
    return {
      recommendations: [
        {
          type: 'best_value',
          title: 'Best Value Option',
          description: 'Great balance of price and convenience',
          confidence: 0.85
        },
        {
          type: 'fastest',
          title: 'Fastest Route',
          description: 'Shortest travel time',
          confidence: 0.90
        },
        {
          type: 'premium',
          title: 'Premium Experience',
          description: 'Best comfort and amenities',
          confidence: 0.80
        }
      ],
      reasoning: response.substring(0, 200) + '...'
    };
  }

  parseHotelRecommendations(response) {
    return {
      recommendations: [
        {
          type: 'luxury',
          title: 'Luxury Choice',
          description: 'Premium amenities and service',
          confidence: 0.88
        },
        {
          type: 'value',
          title: 'Best Value',
          description: 'Great amenities at reasonable price',
          confidence: 0.92
        },
        {
          type: 'location',
          title: 'Prime Location',
          description: 'Perfect location for sightseeing',
          confidence: 0.85
        }
      ],
      reasoning: response.substring(0, 200) + '...'
    };
  }

  parsePricePrediction(response) {
    return {
      direction: 'stable', // up, down, stable
      confidence: 0.75,
      percentage: 5,
      reasoning: response.substring(0, 150) + '...',
      recommendation: 'Monitor prices for 2-3 more days before booking'
    };
  }

  parseSearchQuery(response) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      logger.error('Error parsing search query:', error);
    }
    return null;
  }

  parseTravelInsights(response) {
    return {
      weather: 'Pleasant weather expected',
      attractions: ['Popular landmarks', 'Local markets', 'Cultural sites'],
      tips: ['Book attractions in advance', 'Try local cuisine', 'Respect local customs'],
      budget: 'Moderate budget recommended',
      safety: 'Generally safe for tourists',
      fullInsights: response
    };
  }

  generateSuggestions(userMessage) {
    const message = userMessage.toLowerCase();
    
    if (message.includes('book') || message.includes('reservation')) {
      return ['Search Flights', 'Search Hotels', 'View Bookings'];
    } else if (message.includes('cancel') || message.includes('modify')) {
      return ['My Bookings', 'Cancellation Policy', 'Contact Support'];
    } else if (message.includes('price') || message.includes('cost')) {
      return ['Price Alerts', 'Best Deals', 'Compare Prices'];
    } else {
      return ['Search Flights', 'Search Hotels', 'Help Center'];
    }
  }

  // Fallback responses when AI is not available
  getFallbackFlightRecommendations() {
    return {
      recommendations: [
        {
          type: 'popular',
          title: 'Popular Choice',
          description: 'Frequently booked by other travelers',
          confidence: 0.70
        }
      ],
      reasoning: 'Based on popular booking patterns'
    };
  }

  getFallbackHotelRecommendations() {
    return {
      recommendations: [
        {
          type: 'popular',
          title: 'Highly Rated',
          description: 'Top-rated hotels in the area',
          confidence: 0.70
        }
      ],
      reasoning: 'Based on customer reviews and ratings'
    };
  }

  getFallbackPricePrediction() {
    return {
      direction: 'stable',
      confidence: 0.60,
      percentage: 0,
      reasoning: 'Historical data suggests stable pricing',
      recommendation: 'Current prices are reasonable for booking'
    };
  }

  getFallbackTravelInsights() {
    return {
      weather: 'Check local weather forecast',
      attractions: ['Research popular attractions', 'Check local events'],
      tips: ['Book accommodations early', 'Check visa requirements'],
      budget: 'Research local costs',
      safety: 'Check travel advisories',
      fullInsights: 'Please check official travel resources for detailed information.'
    };
  }
}

module.exports = new AIService();
