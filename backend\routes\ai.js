const express = require("express");
const { protect, authorize, optionalAuth } = require("../middleware/auth");
const aiController = require("../controllers/aiController");

const router = express.Router();

// Flight recommendations
router.get(
  "/recommendations/flights",
  protect,
  aiController.getFlightRecommendations
);

// Hotel recommendations
router.get(
  "/recommendations/hotels",
  protect,
  aiController.getHotelRecommendations
);

// Recommendations history
router.get(
  "/recommendations/history",
  protect,
  aiController.getRecommendationsHistory
);

// Track recommendation interaction
router.post(
  "/recommendations/:id/interact",
  protect,
  aiController.trackRecommendationInteraction
);

// Price predictions
router.get(
  "/price-predictions/:flightId",
  protect,
  aiController.getPricePredictions
);

// AI Chat
router.post("/chat", protect, aiController.chatWithAI);

// Natural language search
router.post(
  "/search/natural",
  optionalAuth,
  aiController.processNaturalLanguageSearch
);

// Travel insights
router.get(
  "/insights/:destination",
  optionalAuth,
  aiController.getTravelInsights
);

// AI Analytics (Admin only)
router.get(
  "/analytics",
  protect,
  authorize("admin"),
  aiController.getAIAnalytics
);

module.exports = router;
