const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Import utilities and config
const connectDB = require("./config/database");
const logger = require("./utils/logger");

// Create Express app
const app = express();

// Connect to database
connectDB();

// Basic middleware
app.use(
  cors({
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    credentials: true,
  })
);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: "1.0.0",
  });
});

// Import routes
const authRoutes = require("./routes/auth");
const paymentRoutes = require("./routes/payments");

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/payments", paymentRoutes);

// Basic API routes
app.get("/api/test", (req, res) => {
  res.status(200).json({
    success: true,
    message: "API is working!",
    data: {
      server: "MakemyTrip Backend",
      version: "1.0.0",
      timestamp: new Date().toISOString(),
    },
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

// Basic error handler
app.use((err, req, res, next) => {
  logger.error("Error:", err);
  res.status(500).json({
    success: false,
    message: "Server Error",
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  logger.info(
    `Server running in ${process.env.NODE_ENV || "development"} mode on port ${PORT}`
  );
});

module.exports = app;
