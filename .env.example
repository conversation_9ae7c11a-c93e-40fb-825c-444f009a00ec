# Database
MONGODB_URI=mongodb://localhost:27017/makemytrip
MONGODB_URI_PROD=mongodb+srv://username:<EMAIL>/makemytrip

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Server
PORT=5000
NODE_ENV=development

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Payment Gateway
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# AI Services
OPENAI_API_KEY=your_openai_api_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# External APIs
AMADEUS_API_KEY=your_amadeus_api_key
AMADEUS_API_SECRET=your_amadeus_api_secret

# Frontend URL
CLIENT_URL=http://localhost:3000

# Production Database Configuration
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-password
MONGO_DB_NAME=makemytrip

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Additional JWT Configuration
JWT_COOKIE_EXPIRE=30

# Email Configuration (Production - SendGrid)
EMAIL_HOST_PROD=smtp.sendgrid.net
EMAIL_PORT_PROD=587
EMAIL_USER_PROD=apikey
EMAIL_PASS_PROD=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=MakemyTrip

# Stripe Webhook
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your-session-secret

# Monitoring and Logging
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn

# Social Login (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_SOCIAL_LOGIN=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
