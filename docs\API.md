# MakemyTrip API Documentation

This document provides comprehensive information about the MakemyTrip API endpoints, request/response formats, and authentication requirements.

## Base URL

```
Development: http://localhost:5000/api
Production: https://api.yourdomain.com/api
```

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 100,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

## Error Handling

Error responses include:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

## Authentication Endpoints

### Register User

**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "Password123",
  "phone": "+**********",
  "dateOfBirth": "1990-01-01",
  "gender": "male"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

### Login User

**POST** `/auth/login`

Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

### Get Current User

**GET** `/auth/me`

Get current authenticated user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "preferences": {
      "currency": "USD",
      "language": "en"
    },
    "loyaltyProgram": {
      "tier": "bronze",
      "points": 1250
    }
  }
}
```

## Flight Endpoints

### Search Flights

**GET** `/flights/search`

Search for available flights.

**Query Parameters:**
- `from` (required): Departure airport code
- `to` (required): Arrival airport code
- `departureDate` (required): Departure date (YYYY-MM-DD)
- `returnDate` (optional): Return date for round trip
- `passengers` (optional): Number of passengers (default: 1)
- `travelClass` (optional): economy, premium_economy, business, first
- `maxPrice` (optional): Maximum price filter
- `airline` (optional): Airline code filter
- `stops` (optional): Number of stops (0, 1, 2+)
- `sortBy` (optional): price, duration, departure_time, arrival_time
- `sortOrder` (optional): asc, desc
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Example Request:**
```
GET /flights/search?from=JFK&to=LAX&departureDate=2024-12-25&passengers=2&travelClass=economy
```

**Response:**
```json
{
  "success": true,
  "data": {
    "flights": [
      {
        "id": "flight_id",
        "flightNumber": "AA1234",
        "airline": {
          "code": "AA",
          "name": "American Airlines",
          "logo": "https://example.com/aa-logo.png"
        },
        "route": {
          "departure": {
            "airport": {
              "code": "JFK",
              "name": "John F. Kennedy International Airport",
              "city": "New York",
              "country": "USA"
            },
            "dateTime": "2024-12-25T14:30:00Z",
            "terminal": "4",
            "gate": "A12"
          },
          "arrival": {
            "airport": {
              "code": "LAX",
              "name": "Los Angeles International Airport",
              "city": "Los Angeles",
              "country": "USA"
            },
            "dateTime": "2024-12-25T18:45:00Z",
            "terminal": "6",
            "gate": "B8"
          },
          "duration": 375,
          "distance": 3944,
          "stops": []
        },
        "pricing": {
          "economy": {
            "currentPrice": 329,
            "availableSeats": 45,
            "taxes": 45,
            "fees": 25
          }
        },
        "amenities": {
          "wifi": true,
          "meals": true,
          "entertainment": true,
          "powerOutlets": true
        }
      }
    ],
    "searchParams": {
      "from": "JFK",
      "to": "LAX",
      "departureDate": "2024-12-25",
      "passengers": 2,
      "travelClass": "economy"
    }
  },
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalFlights": 47,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### Get Flight Details

**GET** `/flights/:id`

Get detailed information about a specific flight.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "flight_id",
    "flightNumber": "AA1234",
    "airline": {
      "code": "AA",
      "name": "American Airlines",
      "logo": "https://example.com/aa-logo.png"
    },
    "aircraft": {
      "type": "Boeing 737-800",
      "model": "737-800",
      "capacity": {
        "economy": 150,
        "premiumEconomy": 20,
        "business": 16,
        "first": 8
      }
    },
    "route": {
      "departure": {
        "airport": {
          "code": "JFK",
          "name": "John F. Kennedy International Airport",
          "city": "New York",
          "country": "USA",
          "timezone": "America/New_York"
        },
        "terminal": "4",
        "gate": "A12",
        "dateTime": "2024-12-25T14:30:00Z"
      },
      "arrival": {
        "airport": {
          "code": "LAX",
          "name": "Los Angeles International Airport",
          "city": "Los Angeles",
          "country": "USA",
          "timezone": "America/Los_Angeles"
        },
        "terminal": "6",
        "gate": "B8",
        "dateTime": "2024-12-25T18:45:00Z"
      },
      "duration": 375,
      "distance": 3944,
      "stops": []
    },
    "pricing": {
      "economy": {
        "basePrice": 299,
        "currentPrice": 329,
        "availableSeats": 45,
        "taxes": 45,
        "fees": 25
      },
      "business": {
        "basePrice": 1299,
        "currentPrice": 1399,
        "availableSeats": 3,
        "taxes": 125,
        "fees": 75
      }
    },
    "amenities": {
      "wifi": true,
      "meals": true,
      "entertainment": true,
      "powerOutlets": true,
      "extraLegroom": false
    },
    "baggage": {
      "carryOn": {
        "included": true,
        "weight": 10,
        "dimensions": "56x45x25cm"
      },
      "checked": {
        "included": false,
        "weight": 23,
        "additionalFee": 30
      }
    },
    "policies": {
      "cancellation": {
        "allowed": true,
        "fee": 150,
        "timeLimit": 24
      },
      "modification": {
        "allowed": true,
        "fee": 75,
        "timeLimit": 24
      }
    }
  }
}
```

## Hotel Endpoints

### Search Hotels

**GET** `/hotels/search`

Search for available hotels.

**Query Parameters:**
- `destination` (required): City or location
- `checkIn` (required): Check-in date (YYYY-MM-DD)
- `checkOut` (required): Check-out date (YYYY-MM-DD)
- `adults` (optional): Number of adults (default: 2)
- `children` (optional): Number of children (default: 0)
- `rooms` (optional): Number of rooms (default: 1)
- `minPrice` (optional): Minimum price filter
- `maxPrice` (optional): Maximum price filter
- `starRating` (optional): Hotel star rating (1-5)
- `amenities` (optional): Comma-separated amenities
- `sortBy` (optional): price, rating, distance
- `sortOrder` (optional): asc, desc
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Example Request:**
```
GET /hotels/search?destination=New York&checkIn=2024-12-25&checkOut=2024-12-27&adults=2&rooms=1
```

## Booking Endpoints

### Create Booking

**POST** `/bookings`

Create a new booking.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "type": "flight",
  "flightId": "flight_id",
  "passengers": [
    {
      "title": "Mr",
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01",
      "gender": "male",
      "passportNumber": "A12345678",
      "nationality": "US"
    }
  ],
  "travelClass": "economy",
  "contactInfo": {
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "specialRequests": "Vegetarian meal"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "bookingId": "BK123456789",
    "status": "pending",
    "type": "flight",
    "flight": {
      "flightNumber": "AA1234",
      "route": {
        "departure": {
          "airport": "JFK",
          "dateTime": "2024-12-25T14:30:00Z"
        },
        "arrival": {
          "airport": "LAX",
          "dateTime": "2024-12-25T18:45:00Z"
        }
      }
    },
    "passengers": [
      {
        "title": "Mr",
        "firstName": "John",
        "lastName": "Doe"
      }
    ],
    "pricing": {
      "baseAmount": 299,
      "taxes": 45,
      "fees": 25,
      "totalAmount": 369
    },
    "payment": {
      "status": "pending",
      "method": null
    },
    "createdAt": "2024-12-20T10:00:00Z"
  }
}
```

## AI Endpoints

### Get AI Recommendations

**GET** `/ai/recommendations/flights`

Get AI-powered flight recommendations.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `from` (required): Departure airport
- `to` (required): Destination airport
- `departureDate` (required): Travel date
- `passengers` (optional): Number of passengers
- `travelClass` (optional): Travel class

**Response:**
```json
{
  "success": true,
  "data": {
    "recommendations": [
      {
        "type": "best_value",
        "title": "Best Value Option",
        "description": "Great balance of price and convenience",
        "confidence": 0.85
      },
      {
        "type": "fastest",
        "title": "Fastest Route",
        "description": "Shortest travel time",
        "confidence": 0.90
      }
    ],
    "reasoning": "Based on your travel history and preferences..."
  }
}
```

### AI Chat

**POST** `/ai/chat`

Chat with AI assistant.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "message": "I need help finding a flight to Paris",
  "conversationHistory": [
    {
      "role": "user",
      "content": "Hello"
    },
    {
      "role": "assistant",
      "content": "Hi! How can I help you today?"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "I'd be happy to help you find a flight to Paris! Could you please tell me your departure city and preferred travel dates?",
    "suggestions": ["Search Flights", "Popular Destinations", "Travel Tips"],
    "timestamp": "2024-12-20T10:00:00Z"
  }
}
```

## Payment Endpoints

### Create Payment Intent

**POST** `/payments/create-intent`

Create a payment intent for booking.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "amount": 369.00,
  "currency": "usd",
  "bookingData": {
    "type": "flight",
    "baseAmount": 299,
    "taxes": 45,
    "fees": 25,
    "totalAmount": 369
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "clientSecret": "pi_**********_secret_abcdef",
    "paymentIntentId": "pi_**********",
    "bookingId": "booking_id",
    "amount": 36900,
    "currency": "usd"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Invalid input data |
| 401  | Unauthorized - Invalid or missing token |
| 403  | Forbidden - Insufficient permissions |
| 404  | Not Found - Resource not found |
| 409  | Conflict - Resource already exists |
| 422  | Unprocessable Entity - Validation failed |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error - Server error |

## Rate Limiting

API requests are limited to:
- **Authenticated users**: 1000 requests per hour
- **Unauthenticated users**: 100 requests per hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination with these parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

Pagination info is included in the response:
```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 100,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

## Webhooks

### Stripe Webhooks

**POST** `/payments/webhook`

Handles Stripe webhook events for payment processing.

**Headers:**
- `Stripe-Signature`: Webhook signature for verification

**Events Handled:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `charge.dispute.created`

For more detailed information about specific endpoints, please refer to the inline API documentation or contact the development team.
