{"name": "makemytrip-backend", "version": "1.0.0", "description": "Backend API for MakemyTrip clone", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedData.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "stripe": "^14.7.0", "nodemailer": "^6.9.7", "axios": "^1.6.2", "openai": "^4.20.1", "socket.io": "^4.7.4", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "api", "travel", "booking"], "author": "Your Name", "license": "MIT"}