apiVersion: v1
kind: ConfigMap
metadata:
  name: makemytrip-config
  namespace: makemytrip
data:
  NODE_ENV: "production"
  PORT: "5000"
  JWT_EXPIRE: "30d"
  JWT_COOKIE_EXPIRE: "30"
  EMAIL_HOST: "smtp.sendgrid.net"
  EMAIL_PORT: "587"
  FROM_EMAIL: "<EMAIL>"
  FROM_NAME: "MakemyTrip"
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  BCRYPT_SALT_ROUNDS: "12"
  LOG_LEVEL: "info"
  ENABLE_AI_FEATURES: "true"
  ENABLE_SOCIAL_LOGIN: "true"
  ENABLE_EMAIL_NOTIFICATIONS: "true"
  ENABLE_SMS_NOTIFICATIONS: "false"
