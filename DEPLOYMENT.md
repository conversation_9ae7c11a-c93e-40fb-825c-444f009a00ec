# MakemyTrip Deployment Guide

This guide covers the deployment of the MakemyTrip application to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Setup](#database-setup)
4. [Backend Deployment](#backend-deployment)
5. [Frontend Deployment](#frontend-deployment)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Security Considerations](#security-considerations)
9. [Performance Optimization](#performance-optimization)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account or self-hosted MongoDB
- Stripe account for payments
- OpenAI API key for AI features
- Domain name and SSL certificate
- Cloud hosting provider (AWS, Google Cloud, Azure, or DigitalOcean)

## Environment Setup

### Backend Environment Variables

Create a `.env` file in the backend directory:

```env
# Server Configuration
NODE_ENV=production
PORT=5000

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/makemytrip

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30

# Email Configuration (using SendGrid)
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASS=your-sendgrid-api-key

# Payment Configuration
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# AI Configuration
OPENAI_API_KEY=sk-your-openai-api-key

# File Upload
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Client URL
CLIENT_URL=https://yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Frontend Environment Variables

Create a `.env` file in the frontend directory:

```env
# API Configuration
REACT_APP_API_URL=https://api.yourdomain.com/api

# Payment Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key

# Analytics (optional)
REACT_APP_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Feature Flags
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_ENABLE_SOCIAL_LOGIN=true
```

## Database Setup

### MongoDB Atlas Setup

1. Create a MongoDB Atlas cluster
2. Configure network access (whitelist your server IPs)
3. Create a database user with read/write permissions
4. Get the connection string

### Database Initialization

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Seed the database with sample data
npm run seed
```

## Backend Deployment

### Option 1: Deploy to Heroku

1. Install Heroku CLI
2. Create a new Heroku app:

```bash
heroku create makemytrip-api
```

3. Set environment variables:

```bash
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your-mongodb-uri
heroku config:set JWT_SECRET=your-jwt-secret
# ... set all other environment variables
```

4. Deploy:

```bash
git add .
git commit -m "Deploy to production"
git push heroku main
```

### Option 2: Deploy to DigitalOcean App Platform

1. Create a new app on DigitalOcean
2. Connect your GitHub repository
3. Configure build and run commands:
   - Build Command: `npm install`
   - Run Command: `npm start`
4. Set environment variables in the app settings
5. Deploy

### Option 3: Deploy to AWS EC2

1. Launch an EC2 instance (Ubuntu 20.04 LTS)
2. Install Node.js and npm:

```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

3. Install PM2 for process management:

```bash
sudo npm install -g pm2
```

4. Clone your repository and install dependencies:

```bash
git clone https://github.com/yourusername/makemytrip.git
cd makemytrip/backend
npm install
```

5. Create PM2 ecosystem file:

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'makemytrip-api',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    }
  }]
};
```

6. Start the application:

```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

7. Configure Nginx as reverse proxy:

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Frontend Deployment

### Option 1: Deploy to Netlify

1. Build the application:

```bash
cd frontend
npm run build
```

2. Deploy to Netlify:
   - Drag and drop the `build` folder to Netlify
   - Or connect your GitHub repository for automatic deployments

3. Configure redirects for SPA routing:

Create `public/_redirects` file:

```
/*    /index.html   200
```

### Option 2: Deploy to Vercel

1. Install Vercel CLI:

```bash
npm install -g vercel
```

2. Deploy:

```bash
cd frontend
vercel --prod
```

### Option 3: Deploy to AWS S3 + CloudFront

1. Build the application:

```bash
npm run build
```

2. Create S3 bucket and upload build files
3. Configure CloudFront distribution
4. Set up custom domain and SSL certificate

## CI/CD Pipeline

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      # Backend tests
      - name: Install backend dependencies
        run: cd backend && npm install
      - name: Run backend tests
        run: cd backend && npm test
      
      # Frontend tests
      - name: Install frontend dependencies
        run: cd frontend && npm install
      - name: Run frontend tests
        run: cd frontend && npm test

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.12.12
        with:
          heroku_api_key: ${{secrets.HEROKU_API_KEY}}
          heroku_app_name: "makemytrip-api"
          heroku_email: "<EMAIL>"
          appdir: "backend"

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install and build
        run: cd frontend && npm install && npm run build
      - name: Deploy to Netlify
        uses: nwtgck/actions-netlify@v1.2
        with:
          publish-dir: './frontend/build'
          production-branch: main
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
```

## Monitoring and Logging

### Backend Monitoring

1. **Application Performance Monitoring (APM)**:
   - Use New Relic, DataDog, or AppDynamics
   - Monitor response times, error rates, and throughput

2. **Error Tracking**:
   - Integrate Sentry for error tracking
   - Set up alerts for critical errors

3. **Health Checks**:
   - Implement health check endpoints
   - Monitor database connectivity

### Logging

1. **Structured Logging**:
   - Use Winston with JSON format
   - Include correlation IDs for request tracing

2. **Log Aggregation**:
   - Use ELK Stack (Elasticsearch, Logstash, Kibana)
   - Or cloud solutions like AWS CloudWatch

## Security Considerations

1. **HTTPS**: Always use SSL/TLS certificates
2. **Environment Variables**: Never commit secrets to version control
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Input Validation**: Validate all user inputs
5. **CORS**: Configure CORS properly for production
6. **Security Headers**: Use helmet.js for security headers
7. **Database Security**: Use MongoDB Atlas with IP whitelisting
8. **API Keys**: Rotate API keys regularly

## Performance Optimization

### Backend Optimization

1. **Caching**: Implement Redis for caching
2. **Database Indexing**: Create proper indexes for queries
3. **Connection Pooling**: Configure MongoDB connection pooling
4. **Compression**: Enable gzip compression
5. **CDN**: Use CDN for static assets

### Frontend Optimization

1. **Code Splitting**: Implement lazy loading
2. **Bundle Optimization**: Analyze and optimize bundle size
3. **Image Optimization**: Use WebP format and lazy loading
4. **Caching**: Implement service workers for caching
5. **Performance Monitoring**: Use Lighthouse and Web Vitals

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Check MongoDB URI and network access
   - Verify database user permissions

2. **Environment Variables**:
   - Ensure all required environment variables are set
   - Check for typos in variable names

3. **CORS Issues**:
   - Verify CORS configuration
   - Check if frontend URL is whitelisted

4. **Payment Issues**:
   - Verify Stripe keys (test vs live)
   - Check webhook endpoint configuration

5. **Build Failures**:
   - Check Node.js version compatibility
   - Verify all dependencies are installed

### Debugging Commands

```bash
# Check application logs
pm2 logs makemytrip-api

# Monitor application status
pm2 status

# Restart application
pm2 restart makemytrip-api

# Check database connectivity
mongo "mongodb+srv://cluster.mongodb.net/test" --username username

# Test API endpoints
curl -X GET https://api.yourdomain.com/health
```

## Maintenance

1. **Regular Updates**: Keep dependencies updated
2. **Security Patches**: Apply security patches promptly
3. **Database Backups**: Set up automated backups
4. **Performance Monitoring**: Monitor and optimize performance
5. **Log Rotation**: Implement log rotation to manage disk space

For additional support, refer to the project documentation or contact the development team.
