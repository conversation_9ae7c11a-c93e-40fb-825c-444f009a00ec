import React, { useState, useEffect } from "react";
import {
  Container,
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
} from "@mui/material";
import {
  Flight,
  Schedule,
  AttachMoney,
  FlightTakeoff,
  FlightLand,
  AccessTime,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";

// Components
import FlightSearchForm from "../components/Search/FlightSearchForm";
import AIRecommendations from "../components/AI/AIRecommendations";
import SmartSearch from "../components/AI/SmartSearch";

// Actions
import { searchFlights, clearFlights } from "../store/slices/flightSlice";

const FlightSearch = () => {
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();

  const {
    flights,
    loading,
    error,
    searchParams: reduxSearchParams,
    pagination,
  } = useSelector((state) => state.flights);

  const [sortBy, setSortBy] = useState("price");
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    // If URL has search params, use them
    const urlParams = {
      from: searchParams.get("from"),
      to: searchParams.get("to"),
      departureDate: searchParams.get("departureDate"),
      returnDate: searchParams.get("returnDate"),
      passengers: searchParams.get("passengers") || 1,
      travelClass: searchParams.get("travelClass") || "economy",
    };

    // If we have valid search params, search for flights
    if (urlParams.from && urlParams.to && urlParams.departureDate) {
      dispatch(searchFlights({ ...urlParams, page: currentPage, sortBy }));
    } else if (reduxSearchParams.from && reduxSearchParams.to) {
      // Use Redux search params if available
      dispatch(
        searchFlights({ ...reduxSearchParams, page: currentPage, sortBy })
      );
    }

    return () => {
      // Clear flights when component unmounts
      dispatch(clearFlights());
    };
  }, [dispatch, searchParams, reduxSearchParams, currentPage, sortBy]);

  const handleSortChange = (event) => {
    setSortBy(event.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateTime) => {
    return new Date(dateTime).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  const formatDate = (dateTime) => {
    return new Date(dateTime).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getStopsText = (stops) => {
    if (!stops || stops.length === 0) return "Non-stop";
    if (stops.length === 1) return "1 stop";
    return `${stops.length} stops`;
  };

  const handleSmartSearchResult = (parsedQuery) => {
    // Handle the parsed query from smart search
    console.log("Smart search result:", parsedQuery);
    // You can dispatch actions here to update the search form
  };

  const handleRecommendationSelect = (recommendation) => {
    // Handle AI recommendation selection
    console.log("Recommendation selected:", recommendation);
    // You can apply the recommendation to the search
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Smart Search */}
      <SmartSearch
        onSearchResult={handleSmartSearchResult}
        placeholder="Try: 'Find me a cheap flight from New York to London next week'"
      />

      {/* Search Form */}
      <Paper elevation={2} sx={{ mb: 4 }}>
        <FlightSearchForm />
      </Paper>

      {/* AI Recommendations */}
      {reduxSearchParams.from && reduxSearchParams.to && (
        <AIRecommendations
          type="flight"
          searchParams={reduxSearchParams}
          onRecommendationSelect={handleRecommendationSelect}
        />
      )}

      {/* Results Section */}
      {reduxSearchParams.from && reduxSearchParams.to && (
        <Box>
          {/* Search Summary */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
              {reduxSearchParams.from} → {reduxSearchParams.to}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {reduxSearchParams.departureDate} • {reduxSearchParams.passengers}{" "}
              passenger(s) • {reduxSearchParams.travelClass}
            </Typography>
          </Box>

          {/* Filters and Sort */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
            }}
          >
            <Typography variant="h6">
              {pagination?.totalFlights || 0} flights found
            </Typography>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Sort by</InputLabel>
              <Select
                value={sortBy}
                label="Sort by"
                onChange={handleSortChange}
              >
                <MenuItem value="price">Price</MenuItem>
                <MenuItem value="duration">Duration</MenuItem>
                <MenuItem value="departure">Departure</MenuItem>
                <MenuItem value="arrival">Arrival</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Loading State */}
          {loading && (
            <Box sx={{ display: "flex", justifyContent: "center", py: 8 }}>
              <CircularProgress size={60} />
            </Box>
          )}

          {/* Error State */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Flight Results */}
          {!loading && flights.length > 0 && (
            <Grid container spacing={3}>
              {flights.map((flight, index) => (
                <Grid item xs={12} key={flight._id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card
                      sx={{
                        "&:hover": {
                          boxShadow: 6,
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease-in-out",
                        },
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Grid container spacing={3} alignItems="center">
                          {/* Airline Info */}
                          <Grid item xs={12} md={2}>
                            <Box sx={{ textAlign: "center" }}>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                {flight.airline.code}
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {flight.airline.name}
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {flight.flightNumber}
                              </Typography>
                            </Box>
                          </Grid>

                          {/* Flight Details */}
                          <Grid item xs={12} md={6}>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                              }}
                            >
                              {/* Departure */}
                              <Box sx={{ textAlign: "center" }}>
                                <Typography
                                  variant="h5"
                                  sx={{ fontWeight: 600 }}
                                >
                                  {formatTime(flight.route.departure.dateTime)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {flight.route.departure.airport.code}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {formatDate(flight.route.departure.dateTime)}
                                </Typography>
                              </Box>

                              {/* Flight Path */}
                              <Box sx={{ flex: 1, mx: 2, textAlign: "center" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    mb: 1,
                                  }}
                                >
                                  <FlightTakeoff
                                    sx={{ color: "primary.main", mr: 1 }}
                                  />
                                  <Box
                                    sx={{
                                      flex: 1,
                                      height: 2,
                                      backgroundColor: "primary.main",
                                      mx: 1,
                                    }}
                                  />
                                  <FlightLand
                                    sx={{ color: "primary.main", ml: 1 }}
                                  />
                                </Box>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {formatDuration(flight.route.duration)}
                                </Typography>
                                <Chip
                                  label={getStopsText(flight.route.stops)}
                                  size="small"
                                  variant="outlined"
                                  sx={{ mt: 0.5 }}
                                />
                              </Box>

                              {/* Arrival */}
                              <Box sx={{ textAlign: "center" }}>
                                <Typography
                                  variant="h5"
                                  sx={{ fontWeight: 600 }}
                                >
                                  {formatTime(flight.route.arrival.dateTime)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {flight.route.arrival.airport.code}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {formatDate(flight.route.arrival.dateTime)}
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>

                          {/* Price and Book */}
                          <Grid item xs={12} md={4}>
                            <Box sx={{ textAlign: "center" }}>
                              <Typography
                                variant="h4"
                                color="primary"
                                sx={{ fontWeight: 600 }}
                              >
                                $
                                {flight.pricing[reduxSearchParams.travelClass]
                                  ?.currentPrice || "N/A"}
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                gutterBottom
                              >
                                per person
                              </Typography>

                              <Button
                                variant="contained"
                                size="large"
                                fullWidth
                                sx={{ mt: 2 }}
                                disabled={
                                  !flight.pricing[reduxSearchParams.travelClass]
                                    ?.availableSeats
                                }
                              >
                                {flight.pricing[reduxSearchParams.travelClass]
                                  ?.availableSeats > 0
                                  ? "Select Flight"
                                  : "Sold Out"}
                              </Button>

                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mt: 1 }}
                              >
                                {
                                  flight.pricing[reduxSearchParams.travelClass]
                                    ?.availableSeats
                                }{" "}
                                seats left
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>

                        {/* Additional Info */}
                        <Divider sx={{ my: 2 }} />
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Box sx={{ display: "flex", gap: 2 }}>
                            {flight.amenities.wifi && (
                              <Chip label="WiFi" size="small" />
                            )}
                            {flight.amenities.meals && (
                              <Chip label="Meals" size="small" />
                            )}
                            {flight.amenities.entertainment && (
                              <Chip label="Entertainment" size="small" />
                            )}
                          </Box>
                          <Button size="small" color="primary">
                            View Details
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          )}

          {/* No Results */}
          {!loading &&
            flights.length === 0 &&
            !error &&
            reduxSearchParams.from &&
            reduxSearchParams.to && (
              <Box sx={{ textAlign: "center", py: 8 }}>
                <Flight sx={{ fontSize: 80, color: "text.secondary", mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  No flights found
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Try adjusting your search criteria or dates
                </Typography>
              </Box>
            )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <Pagination
                count={pagination.totalPages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                size="large"
              />
            </Box>
          )}
        </Box>
      )}

      {/* Initial State */}
      {!reduxSearchParams.from && !reduxSearchParams.to && (
        <Box sx={{ textAlign: "center", py: 8 }}>
          <Flight sx={{ fontSize: 80, color: "primary.main", mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Find Your Perfect Flight
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Search and compare flights from hundreds of airlines
          </Typography>
        </Box>
      )}
    </Container>
  );
};

export default FlightSearch;
