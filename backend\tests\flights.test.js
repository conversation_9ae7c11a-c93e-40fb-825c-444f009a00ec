const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server-simple');
const Flight = require('../models/Flight');
const User = require('../models/User');

const MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/makemytrip_test';

describe('Flight Endpoints', () => {
  let authToken;
  let sampleFlight;

  beforeAll(async () => {
    await mongoose.connect(MONGODB_URI);
  });

  beforeEach(async () => {
    // Clear collections
    await Flight.deleteMany({});
    await User.deleteMany({});

    // Create a test user and get auth token
    const userResponse = await request(app)
      .post('/api/auth/register')
      .send({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'Password123',
        phone: '+1234567890',
        dateOfBirth: '1990-01-01',
        gender: 'male'
      });

    authToken = userResponse.body.token;

    // Create a sample flight
    sampleFlight = new Flight({
      flightNumber: 'AA1234',
      airline: {
        code: 'AA',
        name: 'American Airlines',
        logo: 'https://example.com/aa-logo.png'
      },
      aircraft: {
        type: 'Boeing 737-800',
        model: '737-800',
        capacity: {
          economy: 150,
          premiumEconomy: 20,
          business: 16,
          first: 8
        }
      },
      route: {
        departure: {
          airport: {
            code: 'JFK',
            name: 'John F. Kennedy International Airport',
            city: 'New York',
            country: 'USA',
            timezone: 'America/New_York'
          },
          terminal: '4',
          gate: 'A12',
          dateTime: new Date('2024-12-25T14:30:00Z')
        },
        arrival: {
          airport: {
            code: 'LAX',
            name: 'Los Angeles International Airport',
            city: 'Los Angeles',
            country: 'USA',
            timezone: 'America/Los_Angeles'
          },
          terminal: '6',
          gate: 'B8',
          dateTime: new Date('2024-12-25T18:45:00Z')
        },
        duration: 375,
        distance: 3944,
        stops: []
      },
      pricing: {
        economy: {
          basePrice: 299,
          currentPrice: 329,
          availableSeats: 45,
          taxes: 45,
          fees: 25
        },
        business: {
          basePrice: 1299,
          currentPrice: 1399,
          availableSeats: 3,
          taxes: 125,
          fees: 75
        }
      },
      amenities: {
        wifi: true,
        meals: true,
        entertainment: true,
        powerOutlets: true,
        extraLegroom: false
      },
      status: 'scheduled',
      isActive: true
    });

    await sampleFlight.save();
  });

  afterAll(async () => {
    await Flight.deleteMany({});
    await User.deleteMany({});
    await mongoose.connection.close();
  });

  describe('GET /api/flights/search', () => {
    it('should search flights with valid parameters', async () => {
      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'JFK',
          to: 'LAX',
          departureDate: '2024-12-25',
          passengers: 1,
          travelClass: 'economy'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flights).toHaveLength(1);
      expect(response.body.data.flights[0].flightNumber).toBe('AA1234');
      expect(response.body.data.pagination).toBeDefined();
    });

    it('should return empty results for non-existent route', async () => {
      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'XYZ',
          to: 'ABC',
          departureDate: '2024-12-25',
          passengers: 1,
          travelClass: 'economy'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flights).toHaveLength(0);
    });

    it('should return 400 for missing required parameters', async () => {
      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'JFK'
          // Missing 'to' and other required parameters
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should filter flights by price', async () => {
      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'JFK',
          to: 'LAX',
          departureDate: '2024-12-25',
          passengers: 1,
          travelClass: 'economy',
          maxPrice: 300
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flights).toHaveLength(0); // Price is 329, above max of 300
    });

    it('should sort flights by price', async () => {
      // Create another flight with different price
      const anotherFlight = new Flight({
        ...sampleFlight.toObject(),
        _id: new mongoose.Types.ObjectId(),
        flightNumber: 'DL5678',
        airline: { code: 'DL', name: 'Delta Airlines' },
        pricing: {
          economy: {
            basePrice: 199,
            currentPrice: 229,
            availableSeats: 50,
            taxes: 35,
            fees: 20
          }
        }
      });
      await anotherFlight.save();

      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'JFK',
          to: 'LAX',
          departureDate: '2024-12-25',
          passengers: 1,
          travelClass: 'economy',
          sortBy: 'price',
          sortOrder: 'asc'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flights).toHaveLength(2);
      expect(response.body.data.flights[0].pricing.economy.currentPrice)
        .toBeLessThan(response.body.data.flights[1].pricing.economy.currentPrice);
    });

    it('should check seat availability', async () => {
      const response = await request(app)
        .get('/api/flights/search')
        .query({
          from: 'JFK',
          to: 'LAX',
          departureDate: '2024-12-25',
          passengers: 50, // More than available seats (45)
          travelClass: 'economy'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flights).toHaveLength(0);
    });
  });

  describe('GET /api/flights/:id', () => {
    it('should get flight details by ID', async () => {
      const response = await request(app)
        .get(`/api/flights/${sampleFlight._id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data._id).toBe(sampleFlight._id.toString());
      expect(response.body.data.flightNumber).toBe('AA1234');
    });

    it('should return 404 for non-existent flight ID', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      const response = await request(app)
        .get(`/api/flights/${nonExistentId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Flight not found');
    });

    it('should return 400 for invalid flight ID format', async () => {
      const response = await request(app)
        .get('/api/flights/invalid-id')
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/flights/destinations/popular', () => {
    it('should get popular destinations', async () => {
      const response = await request(app)
        .get('/api/flights/destinations/popular')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('city');
      expect(response.body.data[0]).toHaveProperty('country');
      expect(response.body.data[0]).toHaveProperty('airport');
    });
  });

  describe('GET /api/flights/:id/price-history', () => {
    it('should get price history for a flight', async () => {
      const response = await request(app)
        .get(`/api/flights/${sampleFlight._id}/price-history`)
        .query({ travelClass: 'economy', days: 30 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.flightId).toBe(sampleFlight._id.toString());
      expect(response.body.data.travelClass).toBe('economy');
      expect(response.body.data.currentPrice).toBe(329);
      expect(response.body.data.priceHistory).toBeInstanceOf(Array);
    });

    it('should return 404 for non-existent flight', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      const response = await request(app)
        .get(`/api/flights/${nonExistentId}/price-history`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Flight not found');
    });
  });

  describe('GET /api/flights/airlines', () => {
    it('should get list of airlines', async () => {
      const response = await request(app)
        .get('/api/flights/airlines')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data).toContain(sampleFlight.airline);
    });
  });

  describe('GET /api/flights/airports', () => {
    it('should get list of airports', async () => {
      const response = await request(app)
        .get('/api/flights/airports')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0]).toHaveProperty('code');
      expect(response.body.data[0]).toHaveProperty('name');
      expect(response.body.data[0]).toHaveProperty('city');
    });

    it('should search airports by query', async () => {
      const response = await request(app)
        .get('/api/flights/airports')
        .query({ search: 'JFK' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      
      // Should find JFK airport
      const jfkAirport = response.body.data.find(airport => airport.code === 'JFK');
      expect(jfkAirport).toBeDefined();
    });
  });

  describe('Protected Routes', () => {
    describe('POST /api/flights/:id/book', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .post(`/api/flights/${sampleFlight._id}/book`)
          .send({
            passengers: [{ firstName: 'John', lastName: 'Doe' }],
            travelClass: 'economy'
          })
          .expect(401);

        expect(response.body.success).toBe(false);
      });

      it('should return not implemented with valid auth', async () => {
        const response = await request(app)
          .post(`/api/flights/${sampleFlight._id}/book`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            passengers: [{ firstName: 'John', lastName: 'Doe' }],
            travelClass: 'economy'
          })
          .expect(501);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe('Flight booking not implemented yet');
      });
    });

    describe('GET /api/flights/recommendations', () => {
      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/flights/recommendations')
          .expect(401);

        expect(response.body.success).toBe(false);
      });

      it('should return not implemented with valid auth', async () => {
        const response = await request(app)
          .get('/api/flights/recommendations')
          .set('Authorization', `Bearer ${authToken}`)
          .expect(501);

        expect(response.body.success).toBe(false);
        expect(response.body.message).toBe('Flight recommendations not implemented yet');
      });
    });
  });
});
